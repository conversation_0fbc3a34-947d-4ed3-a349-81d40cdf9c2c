CREATE DATABASE WITNESS;
USE WITNESS;

SET FOREIGN_KEY_CHECKS=0;

DROP TABLE IF EXISTS witness_request;
create TABLE witness_request
(
    id bigint PRIMARY KEY auto_increment COMMENT 'ID',
    client_id VARCHAR(30) NOT NULL COMMENT '客户唯一id',
    state int(1) NOT NULL DEFAULT 0 COMMENT '请求状态：0，等待中；1，撮合成功；-1，客户主动取消请求；-2，无可服务坐席在线；-3，其他原因导致见证请求终止',
    join_time VARCHAR(20) NOT NULL COMMENT '请求生成的时间', -- 开始排队的时间
    priority int(2) NOT NULL DEFAULT 1006 COMMENT '优先级从0开始，0最低级',
    serve_agent_id VARCHAR(10) COMMENT '服务中的坐席唯一id',
    serve_time VARCHAR(20) COMMENT '坐席开始服务的时间',
    serve_end_time VARCHAR(20) COMMENT '坐席结束服务的时间',
    result VARCHAR(10) COMMENT '见证结果是否成功',
    reason VARCHAR(50) COMMENT '见证结果说明',

    -- 客户信息
    client_name varchar(20) COMMENT '客户姓名',
    org int(8) not null COMMENT '营业部',
    biz_type int(5) not null COMMENT '业务类型',
    biz_code VARCHAR(8) COMMENT '业务代码',
    cus_level int(8) NOT NULL DEFAULT 0 COMMENT '客户等级',
    req_data TEXT COMMENT '请求数据', -- 可以从业务系统获取（cif等），落地在请求表中，方便后续查问题

    -- 记录相关业务数据，包括相关派单规则数据，用于排查问题时可追述
    biz_request_id bigint not null COMMENT '业务请求id，用于和业务系统关联',
    marker_data TEXT COMMENT '锚点数据，用于记录见证过程中产生的锚点数据',
    -- 适配的技能组
    tech_code VARCHAR(5) COMMENT '适配的技能组，唯一，对应agent_tech的tech_code字段',
--     tech_code_version int(2) NOT NULL COMMENT '技能组版本，从1开始',
--     rule_code_version int(2) NOT NULL COMMENT '规则版本，从1开始',
--     rule_value_version int(2) NOT NULL COMMENT '规则值版本，从1开始',
    current_queue_info VARCHAR(300) COMMENT '加入队列时的队列排序情况，用请求表id作为元素，用分号隔开'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='见证请求表';

drop table IF EXISTS agent;
create table agent
(
    id bigint PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    agent_id VARCHAR(15) unique not null comment '坐席的账号id',
    agent_name VARCHAR(30) not null comment '坐席名称',
    ENCRYPTED_PASSWORD VARCHAR(255) comment '坐席密码，需要加密',
    org int(12) not null comment '坐席所属营业部',
    techs VARCHAR (50) comment '坐席所属技能，可配置多个'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='坐席表';

DROP TABLE IF EXISTS agent_tech;
create table agent_tech
(
    ID        bigint AUTO_INCREMENT not null comment 'id',
    tech_code     VARCHAR(5) not null comment 'tech code',
    tech_name   VARCHAR(30) not null comment 'tech name',
    tech_type   INTEGER not null comment ' tech type',
    BIZ_LEVEL    VARCHAR(150) not null comment 'business level, multi by ;',
    CUS_LEVEL    VARCHAR(150) not null comment 'customer level(vip level), multi by ;',
    CONSTRAINT tech_unique UNIQUE(ID,tech_code)
--     version     int(2) NOT NULL DEFAULT 1
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='agent tech, means agent can serve the customer who has this bizLevel, this cusLevel and this tech_type';

-- comment on column dispatch_rule_code.version is '坐席技能组版本，从1开始，与tech_code联合唯一';

-- 派单规则代码
DROP TABLE IF EXISTS dispatch_rule_code;
create table dispatch_rule_code
(
    rule_code   VARCHAR(50) not null comment '规则代码',
    rule_name   VARCHAR(50) not null comment '规则名称',
    priority    INTEGER not null comment '优先级',
    rule_dict_type  INTEGER comment '取值方式',
    rule_dict_key    VARCHAR(100) comment '取值参数',
    rule_type   INTEGER not null comment '规则分类',
    html_element_type   INTEGER not null comment '显示控件',
    state       INTEGER not null comment '状态',
    customize_sort_enable   INTEGER not null comment '是否开启自定义排序',
    default_rule     INTEGER comment '是否默认规则，0，不是；1，是，默认为0',
    UNIQUE(rule_code)
--     version     int(2) NOT NULL DEFAULT 1
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='派单规则代码';

-- comment on column dispatch_rule_code.version is '规则版本，从1开始，与rule_code联合唯一';

-- 派单规则参数
DROP TABLE IF EXISTS dispatch_rule_value;
create table dispatch_rule_value
(
    rule_code   VARCHAR(50) not null comment '规则代码',
    rule_value  VARCHAR(30) comment '规则值',
    UNIQUE(rule_code)
--      version     int(2) NOT NULL DEFAULT 1
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='派单规则参数';

-- cowork见证话术维护
DROP TABLE IF EXISTS cowork_script;
create table cowork_script
(
    business_code   VARCHAR(5) not null comment '业务代码',
    initiate_channel  VARCHAR(5) not null comment '发起渠道',
    script TEXT not null comment '话术',
    UNIQUE(business_code)
--      version     int(2) NOT NULL DEFAULT 1
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='见证话术维护';

-- comment on column dispatch_rule_value.version is '规则值版本，从1开始，与rule_code联合唯一';

insert into dispatch_rule_code values ('1001', '见证退回优先派单给原坐席', 1, null, null, 1, 2, 1, 0, 0);
insert into dispatch_rule_code values ('1002', '见证等待超时优先（分钟）', 2, 2, '1|十分钟|10;2|二十分钟|20;3|三十分钟|30', 1, 4, 1, 0, 0);
insert into dispatch_rule_code values ('1003', '客户得分优先', 3, null, null, 1, 2, 1, 0, 0);
insert into dispatch_rule_code values ('1004', '星级客户优先', 4, 1, 'KHXJ', 1, 5, 1, 0, 1);
insert into dispatch_rule_code values ('1005', '业务等级优先', 5, 1, 'BLDJ', 1, 5, 1, 0, 1);
insert into dispatch_rule_code values ('1006', '业务优先提交，优先派单', 6, null, null, 1, 5, 1, 0, 1);

insert into dispatch_rule_code values ('2001', '营业部优先的业务类别', 1, 1, 'ZXJNZ', 2, 3, 1, 0, 0);
insert into dispatch_rule_code values ('2002', '坐席空闲时长，优先派单', 2, null, null, 2, 5, 1, 0, 1);

insert into dispatch_rule_value values ('1002', '1');
insert into dispatch_rule_value values ('1004', '3|2|1|0');
insert into dispatch_rule_value values ('1005', '3|2|1');

insert into dispatch_rule_value values ('2001', '');

-- 客户星级参数
DROP TABLE IF EXISTS customer_level_config;
create table customer_level_config
(
    cus_level        bigint not null comment '客户等级',
    score        INTEGER not null comment '星级得分'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='客户星级参数';

-- 业务等级参数
DROP TABLE IF EXISTS business_level_config;
create table business_level_config
(
    biz_level        bigint not null comment '业务等级',
    score        INTEGER not null comment '等级得分'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='业务等级参数';

-- 业务代码参数
DROP TABLE IF EXISTS business_code;
create table business_code
(
    biz_type    int(5) not null COMMENT '业务类型',
    biz_code    VARCHAR(5) not null comment '业务代码',
    biz_name    VARCHAR(60) comment '业务名称',
    biz_level    bigint comment '业务等级'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='业务代码参数';

-- 呼叫记录表（包括呼叫开始时的数据，坐席响应时的数据），坐席会话变化表，还有派单规则，需要增加修改版本记录

DROP TABLE IF EXISTS witness_script_configuration;
create TABLE witness_script_configuration
(
    id int AUTO_INCREMENT PRIMARY KEY COMMENT '编号',
    biz_type int(5) not null COMMENT '业务类型', -- 该表也作为业务类型的字典表
    biz_type_name VARCHAR(20) not null COMMENT '业务类型名称',
    biz_code VARCHAR(8) COMMENT '业务代码',
    state int(1) not null COMMENT '状态',
    script_modify_date VARCHAR(8) not null COMMENT '话术修改日期',
    script_modify_time VARCHAR(6) not null COMMENT '话术修改时间',
    script_config TEXT not null COMMENT '话术配置，json数组格式',
    UNIQUE (biz_type)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='见证话术配置表';

-- 数据字典
DROP TABLE IF EXISTS witness_dictionary;
create table witness_dictionary (
    dic_code    VARCHAR(30) not null comment '字典代码',
    dic_name    VARCHAR(30) comment '字典名称',
    ibm    INTEGER comment '选项int类型值',
    cbm     VARCHAR(30) comment '选项字符串类型值',
    note    VARCHAR(100) comment '选项名称',
    FLAG    INTEGER comment 'flag'
) comment = 'cif数据字典';

insert into witness_dictionary(dic_code, dic_name, ibm, cbm, note, FLAG) values ('ZXJNZ', '坐席技能组', 1, '1', '开户技能组', 0);
insert into witness_dictionary(dic_code, dic_name, ibm, cbm, note, FLAG) values ('ZXJNZ', '坐席技能组', 2, '2', '二次业务技能组', 0);

-- 还未启用该表
drop table if exists marker_data;
create table marker_data(
    id bigint AUTO_INCREMENT PRIMARY KEY COMMENT '锚点数据的唯一id',
    system_flow_id bigint comment '系统流水id，比如见证流水号、双录流水号等',
    marker_data TEXT COMMENT '锚点数据，用于记录见证或双录产生的锚点数据'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='锚点数据表';

drop table if exists witness_property;
create table witness_property(
    code VARCHAR(40) unique not null comment '参数代码',
    name VARCHAR(40) unique not null comment '参数名称',
    value VARCHAR(100) comment '参数值',
    description VARCHAR(100) COMMENT '参数说明'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='系统参数';

insert into witness_property(code, name, value, description)values ('callTimeout', '呼叫超时时间', '45', '单次呼叫超时时间，单位秒');
insert into witness_property(code, name, value, description)values ('rejectTimeout', '拒绝超时时间', '20', '坐席拒绝后多久才能继续被该客户呼叫，单位秒');

-- 质检参数
drop table if exists witness_quality_check;
create table witness_quality_check (
    code VARCHAR(40) comment '参数代码',
    name VARCHAR(40) comment '参数名称',
    total_score INTEGER comment '总分',
    once_deducted_score INTEGER comment '单次扣分',
    twice_deducted_score INTEGER comment '两次扣分',
    value VARCHAR(100) comment '参数值',
    pass_score INTEGER comment '通过分'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='质检参数表';

insert into witness_quality_check (code, name, total_score, once_deducted_score, twice_deducted_score, pass_score)
values ('KHHDJC', '客户回答检测', 30, 5, 10, 20);
insert into witness_quality_check (code, name, total_score, once_deducted_score, value, pass_score)
values ('RLBD', '人脸比对', 30, 5, '70', 25);
insert into witness_quality_check (code, name, total_score, once_deducted_score, value, pass_score)
values ('ZKRSJC', '在框人数检测', 40, 5, '1', 30);

-- 各种日志表
-- 菜单表
drop table if exists menu;
create table menu
(
    code VARCHAR(2) unique not null comment '菜单code',
    father_code VARCHAR(3) comment '父菜单code',
    name VARCHAR(15) not null comment '菜单名称',
    state       INTEGER not null comment '状态'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='菜单表';

insert into menu (code, name, state) values ('1', '见证配置', 1);
insert into menu (code, name, state) values ('2', '查询统计', 1);
insert into menu (code, name, state) values ('3', '系统管理', 1);
insert into menu (code, father_code, name, state) values ('11', '1', '坐席技能组', 1);
insert into menu (code, father_code, name, state) values ('12', '1', '见证人员管理', 1);
insert into menu (code, father_code, name, state) values ('13', '1', '坐席派单规则', 1);
insert into menu (code, father_code, name, state) values ('14', '1', '见证话术配置', 1);
insert into menu (code, father_code, name, state) values ('21', '2', '坐席调度管理', 1);
insert into menu (code, father_code, name, state) values ('22', '2', '坐席任务查询', 1);
insert into menu (code, father_code, name, state) values ('31', '3', '数据字典管理', 1);
insert into menu (code, father_code, name, state) values ('32', '3', '系统参数管理', 1);

-- 后台操作日志表
drop table if exists manager_operate_log;
create table manager_operate_log
(
    agent_id VARCHAR(15) not null comment '操作员的账号id',
    menu VARCHAR(2) not null comment '操作菜单',
    operate_time DATETIME DEFAULT CURRENT_TIMESTAMP comment '当前操作时间',
    action INTEGER not null comment '操作指令',
    data VARCHAR(200) comment '操作数据',
    response_data VARCHAR(100) comment '操作后的响应数据'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='后台管理操作日志';

insert into witness_dictionary(dic_code, dic_name, ibm, cbm, note, FLAG) values ('MENU_ACTION', '菜单操作指令', 1, '1', '新增', 0);
insert into witness_dictionary(dic_code, dic_name, ibm, cbm, note, FLAG) values ('MENU_ACTION', '菜单操作指令', 2, '2', '删除', 0);
insert into witness_dictionary(dic_code, dic_name, ibm, cbm, note, FLAG) values ('MENU_ACTION', '菜单操作指令', 3, '3', '修改', 0);
insert into witness_dictionary(dic_code, dic_name, ibm, cbm, note, FLAG) values ('MENU_ACTION', '菜单操作指令', 4, '4', '查询', 0);

