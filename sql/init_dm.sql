DROP TABLE witness_request;
create TABLE witness_request
(
    id int PRIMARY KEY IDENTITY COMMENT 'ID',
    client_id VARCHAR(30) NOT NULL COMMENT '客户唯一id',
    state NUMBER(1) NOT NULL DEFAULT 0 COMMENT '请求状态：0，等待中；1，撮合成功；-1，客户主动取消请求；-2，无可服务坐席在线；-3，其他原因导致见证请求终止',
    join_time VARCHAR(20) NOT NULL COMMENT '请求生成的时间', -- 开始排队的时间
    priority NUMBER(2) NOT NULL DEFAULT 0 COMMENT '优先级从0开始，0最低级',
    serve_agent_id VARCHAR(10) COMMENT '服务中的坐席唯一id',
    serve_time VARCHAR(20) COMMENT '坐席开始服务的时间',
    serve_end_time VARCHAR(20) COMMENT '坐席结束服务的时间',
    result VARCHAR(10) COMMENT '见证结果是否成功',
    reason VARCHAR(50) COMMENT '见证结果说明',

    -- 客户信息
    client_name varchar(20) COMMENT '客户姓名',
    org NUMBER(8) not null COMMENT '营业部',
    biz_type NUMBER(5) not null COMMENT '业务类型',
    biz_code VARCHAR(8) COMMENT '业务代码',
    cus_level NUMBER(8) NOT NULL DEFAULT 0 COMMENT '客户等级',
    req_data CLOB COMMENT '请求数据', -- 可以从业务系统获取（cif等），落地在请求表中，方便后续查问题

    -- 记录相关业务数据，包括相关派单规则数据，用于排查问题时可追述
    biz_request_id int not null COMMENT '业务请求id，用于和业务系统关联',
    -- 适配的技能组
    tech_code VARCHAR(5) COMMENT '适配的技能组，唯一，对应agent_tech的tech_code字段',
--     tech_code_version NUMBER(2) NOT NULL COMMENT '技能组版本，从1开始',
--     rule_code_version NUMBER(2) NOT NULL COMMENT '规则版本，从1开始',
--     rule_value_version NUMBER(2) NOT NULL COMMENT '规则值版本，从1开始',
    current_queue_info VARCHAR(300) COMMENT '加入队列时的队列排序情况，用请求表id作为元素，用分号隔开'
) TABLESPACE witness;

COMMENT ON TABLE witness_request IS '见证请求表';

drop table agent;
create table agent
(
    id int PRIMARY KEY IDENTITY COMMENT 'ID',
    agent_id VARCHAR(15) unique not null,
    agent_name VARCHAR(30) unique not null,
    ENCRYPTED_PASSWORD VARCHAR(255),
    org NUMBER(12) not null,
    techs VARCHAR (50)
) TABLESPACE witness;
comment on table agent is '坐席表';
comment on column agent.ID is 'id';
comment on column agent.agent_id is '坐席的账号id';
comment on column agent.agent_name is '坐席名称';
comment on column agent.ENCRYPTED_PASSWORD is '坐席密码，需要加密';
comment on column agent.org is '坐席所属营业部';
comment on column agent.techs is '坐席所属技能，可配置多个';

DROP TABLE agent_tech;
create table agent_tech
(
    id int PRIMARY KEY IDENTITY COMMENT 'ID',
    tech_code     VARCHAR(5) not null,
    tech_name   VARCHAR(30) not null,
    tech_type   NUMBER(4) not null,
    BIZ_LEVEL    VARCHAR(150) not null,
    CUS_LEVEL    VARCHAR(150) not null
--     version     NUMBER(2) NOT NULL DEFAULT 1
) TABLESPACE witness;
comment on table agent_tech is 'agent tech, means agent can serve the customer who has this bizLevel, this cusLevel and this tech_type';
comment on column agent_tech.ID is 'id';
comment on column agent_tech.tech_code is 'tech code';
comment on column agent_tech.tech_name is 'tech name';
comment on column agent_tech.tech_type is 'tech type';
comment on column agent_tech.BIZ_LEVEL is 'business level, multi by ;';
comment on column agent_tech.CUS_LEVEL is 'customer level(vip level), multi by ;';
-- comment on column dispatch_rule_code.version is '坐席技能组版本，从1开始，与tech_code联合唯一';

-- 派单规则代码
DROP TABLE dispatch_rule_code;
create table dispatch_rule_code
(
    rule_code   VARCHAR2(50) not null,
    rule_name   VARCHAR2(50) not null,
    priority    NUMBER(2) not null,
    rule_dict_type  NUMBER(2),
    rule_dict_key    VARCHAR2(100),
    rule_type   NUMBER(1) not null,
    html_element_type   NUMBER(2) not null,
    state       NUMBER(1) not null,
    customize_sort_enable   NUMBER(1) not null,
    default_rule     NUMBER(1)
--     version     NUMBER(2) NOT NULL DEFAULT 1
) TABLESPACE witness;

comment on table dispatch_rule_code is '派单规则代码';
comment on column dispatch_rule_code.rule_code is '规则代码';
comment on column dispatch_rule_code.rule_name is '规则名称';
comment on column dispatch_rule_code.priority is '优先级';
comment on column dispatch_rule_code.rule_dict_type is '取值方式';
comment on column dispatch_rule_code.rule_dict_key is '取值参数';
comment on column dispatch_rule_code.rule_type is '规则分类';
comment on column dispatch_rule_code.html_element_type is '显示控件';
comment on column dispatch_rule_code.state is '状态';
comment on column dispatch_rule_code.customize_sort_enable is '是否开启自定义排序';
comment on column dispatch_rule_code.default_rule is '是否默认规则，0，不是；1，是，默认为0';
-- comment on column dispatch_rule_code.version is '规则版本，从1开始，与rule_code联合唯一';

-- 派单规则参数
DROP TABLE dispatch_rule_value;
create table dispatch_rule_value
(
    rule_code   VARCHAR2(50) not null,
    rule_value  VARCHAR2(30)
--      version     NUMBER(2) NOT NULL DEFAULT 1
) TABLESPACE witness;

comment on table dispatch_rule_value is '派单规则参数';
comment on column dispatch_rule_value.rule_code is '规则代码';
comment on column dispatch_rule_value.rule_value is '规则值';
-- comment on column dispatch_rule_value.version is '规则值版本，从1开始，与rule_code联合唯一';

insert into dispatch_rule_code values ('1001', '见证退回优先派单给原坐席', 1, null, null, 1, 2, 1, 0, 0);
insert into dispatch_rule_code values ('1002', '见证等待超时优先（分钟）', 2, 2, '1|十分钟|10;2|二十分钟|20;3|三十分钟|30', 1, 4, 1, 0, 0);
insert into dispatch_rule_code values ('1003', '客户得分优先', 3, null, null, 1, 2, 1, 0, 0);
insert into dispatch_rule_code values ('1004', '星级客户优先', 4, 1, 'KHXJ', 1, 5, 1, 0, 1);
insert into dispatch_rule_code values ('1005', '业务等级优先', 5, 1, 'BLDJ', 1, 5, 1, 0, 1);
insert into dispatch_rule_code values ('1006', '业务优先提交，优先派单', 6, null, null, 1, 5, 1, 0, 1);

insert into dispatch_rule_code values ('2001', '营业部优先的业务类别', 1, 1, 'ZXJNZ', 2, 3, 1, 0, 0);
insert into dispatch_rule_code values ('2002', '坐席空闲时长，优先派单', 2, null, null, 2, 5, 1, 0, 1);

insert into dispatch_rule_value values ('1002', '1');
insert into dispatch_rule_value values ('1004', '3|2|1|0');
insert into dispatch_rule_value values ('1005', '3|2|1');

insert into dispatch_rule_value values ('2001', '');

-- 客户星级参数
DROP TABLE customer_level_config;
create table customer_level_config
(
    cus_level    NUMBER(8) not null,
    score        NUMBER(4) not null
) TABLESPACE witness;

comment on table customer_level_config is '客户星级参数';
comment on column customer_level_config.cus_level is '客户等级';
comment on column customer_level_config.score is '星级得分';

-- 业务等级参数
DROP TABLE business_level_config;
create table business_level_config
(
    biz_level    NUMBER(8) not null,
    score        NUMBER(4) not null
) TABLESPACE witness;

comment on table business_level_config is '业务等级参数';
comment on column business_level_config.biz_level is '业务等级';
comment on column business_level_config.score is '等级得分';

-- 业务代码参数
DROP TABLE business_code;
create table business_code
(
    biz_code    VARCHAR(8) not null,
    biz_name    VARCHAR(60),
    biz_level    NUMBER(8)
) TABLESPACE witness;

comment on table business_code is '业务代码参数';
comment on column business_code.biz_code is '业务代码';
comment on column business_code.biz_name is '业务名称';
comment on column business_code.biz_level is '业务等级';

-- 呼叫记录表（包括呼叫开始时的数据，坐席响应时的数据），坐席会话变化表，还有派单规则，需要增加修改版本记录

DROP TABLE witness_script_configuration;
create TABLE witness_script_configuration
(
    id int PRIMARY KEY COMMENT '编号',
    biz_type NUMBER(5) not null COMMENT '业务类型', -- 该表也作为业务类型的字典表
    biz_type_name NUMBER(20) not null COMMENT '业务类型名称',
    fps NUMBER(2) COMMENT '帧率，字典项',
    resolution NUMBER(2) COMMENT '分辨率，字典项',
    state NUMBER(1) not null COMMENT '状态',
    script_modify_date VARCHAR(8) not null COMMENT '话术修改日期',
    script_modify_time VARCHAR(6) not null COMMENT '话术修改时间',
    script_config CLOB not null COMMENT '话术配置，json数组格式'
) TABLESPACE witness;

comment on table witness_script_configuration is '见证话术配置表';