{"version": "2.0.0", "tasks": [{"label": "gradle-build", "dependsOn": ["set-permissions", "run-gradle-build"], "dependsOrder": "sequence", "problemMatcher": []}, {"label": "set-permissions", "type": "shell", "command": "chmod +x gradlew", "presentation": {"reveal": "silent", "panel": "dedicated"}}, {"label": "run-gradle-build", "type": "shell", "command": "./gradlew clean build -x test", "presentation": {"reveal": "always", "panel": "dedicated"}, "problemMatcher": []}]}