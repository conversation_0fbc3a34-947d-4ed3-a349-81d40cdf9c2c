{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "启动见证服务",
      "request": "launch",
      "mainClass": "com.apexsoft.AiApplication",
      "projectName": "ai-server-ai-server", // 根据settings.gradle修正项目名
      "cwd": "${workspaceFolder}",
      "javaExec": "/Library/Java/JavaVirtualMachines/jdk1.8.0_431.jdk/Contents/Home/bin/java", // 指定JDK路径
      "vmArgs": "-Dspring.profiles.active=dev",
      "args": "",
      // 调试配置
      "preLaunchTask": "gradle-build", // 如需构建可以再添加
      "console": "internalConsole",
      "stopOnEntry": false,
      "internalConsoleOptions": "neverOpen",
      "sourcePaths": [
        "${workspaceFolder}/ai-server/src/main/java",
        "${workspaceFolder}/core/src/main/java",
        "${workspaceFolder}/dispatcher/src/main/java"
      ]
    }
  ]
}
