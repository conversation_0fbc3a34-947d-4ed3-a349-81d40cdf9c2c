subprojects {
    buildscript {
        repositories {
            maven {
                url "https://maven.aliyun.com/repository/public"
            }
        }
    }
    apply plugin: 'java'
    apply plugin: 'maven-publish'
    ext {
        springBootVersion = "2.6.14"
        springDependencyManagement = "1.0.11.RELEASE"
        springCloudVersion = "2021.0.5"
        liveVersion = "2.5.0-RC7"
    }
    ext["zookeeper.version"]="3.4.14"
    group = 'com.apexsoft'
    version = '1.0.0-SNAPSHOT'
    sourceCompatibility = '1.8'

    repositories {
        maven {
            url "https://maven.aliyun.com/repository/public"
        }
        maven {
            credentials {
                username = "${NEXUS_USER}"
                password = "${NEXUS_PASSWORD}"
            }
            url "https://oss.apexsoft.com.cn/repository/maven-public/"
        }
    }

    //依赖缓存时间
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }
    // 上传source
    task sourcesJar(type: Jar) {
        from sourceSets.main.allJava
        classifier = 'sources'
    }
    publishing {
        publications {
            library(MavenPublication) {
                from components.java
                artifact sourcesJar
            }
        }
        repositories {
            maven {
                credentials {
                    username = "${NEXUS_USER}"
                    password = "${NEXUS_PASSWORD}"
                }
                if (project.version.endsWith('-SNAPSHOT')) {
                    url "https://oss.apexsoft.com.cn/repository/maven-snapshots/"
                } else {
                    url "https://oss.apexsoft.com.cn/repository/maven-releases/"
                }
            }
        }
    }


}