package com.apexsoft.dispatcher.config;

import com.apexsoft.dispatcher.om.AgentSession;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;

@SpringBootConfiguration
public class AgentRedisConfig {

    @Bean("agentRedisTemplate")
    public RedisTemplate<String, AgentSession> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, AgentSession> template = new RedisTemplate<>();
        template.setDefaultSerializer(new Jackson2JsonRedisSerializer<>(AgentSession.class));
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }

}
