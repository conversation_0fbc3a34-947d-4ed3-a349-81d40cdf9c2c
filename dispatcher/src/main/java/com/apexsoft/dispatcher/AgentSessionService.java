package com.apexsoft.dispatcher;

import com.apexsoft.dispatcher.config.Constant;
import com.apexsoft.dispatcher.dispatch.DispatchRuleConfigManager;
import com.apexsoft.dispatcher.dispatch.rule.AbstractDispatchRule;
import com.apexsoft.dispatcher.dispatch.rule.AgentDispatchRule;
import com.apexsoft.dispatcher.dispatch.rule.comparator.AgentComparator;
import com.apexsoft.dispatcher.dispatch.rule.comparator.AnalysisDispatchRule;
import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.DispatchRule;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
@Slf4j
public class AgentSessionService implements Constant {

    @Autowired
    private DispatchRuleConfigManager dispatchRuleConfigManager;

//    @Autowired
//    @Qualifier("agentRedisTemplate")
//    private RedisTemplate<String, AgentSession> redisTemplate;
//    private static String SESSION_PREFIX ;

//    @Value("${app.redis_cache_prefix}")
//    private String redisPrefix;

    private final Map<String, AgentSession> sessionMap = new HashMap<>();

    @Autowired
    private AgentComparator agentComparator;

    private AnalysisDispatchRule<AgentDispatchRule, Boolean> analysisDispatchRule;

    @PostConstruct
    private void init() {
//        SESSION_PREFIX = redisPrefix + "witness_agent_session:";
        this.analysisDispatchRule = new AnalysisDispatchRule<>();
        this.analysisDispatchRule.setRuleType(Constant.RULE_TYPE_AGENT);
        this.analysisDispatchRule.setDispatchRuleClass(AgentDispatchRule.class);
        this.analysisDispatchRule.setDispatchRuleConfigManager(dispatchRuleConfigManager);
        this.analysisDispatchRule.setDefaultResult(true);
        this.analysisDispatchRule.setReturnResult(result -> !result);
    }

//    private HashOperations<String, String, AgentSession> hashOperations() {
//        return redisTemplate.opsForHash();
//    }

    public void addAgentSession (AgentSession agentSession) {
        // 判断下原来的session是否存在，如果存在的话，就不添加了
//        if (!hashOperations().hasKey(SESSION_PREFIX, agentSession.getAgentId())) {
//            hashOperations().put(SESSION_PREFIX, agentSession.getAgentId(), agentSession);
//        }
        sessionMap.put(agentSession.getAgentId(), agentSession);
    }

    public void updateAgentSession(AgentSession agentSession) {
//        hashOperations().put(SESSION_PREFIX, agentSession.getAgentId(), agentSession);
        sessionMap.put(agentSession.getAgentId(), agentSession);
    }

    public void removeAgentSession (String agentId) {
//        hashOperations().delete(SESSION_PREFIX, agentId);
        sessionMap.remove(agentId);
    }

    public Collection<AgentSession> findAllAgentSession() {
//        return hashOperations().values(SESSION_PREFIX);
        return sessionMap.values();
    }

    public AgentSession findByAgentId(String agentId) {
//        return hashOperations().get(SESSION_PREFIX, agentId);
        return sessionMap.get(agentId);
    }

    /**
     * 修改坐席状态到会话中
     * @param agentId 坐席id
     * @param serveRequestId 服务中的请求，-1表示不在服务中，即空闲状态，否则表示在服务中，即繁忙状态
     */
    private void updateAgentState(String agentId, long serveRequestId) {
        AgentSession agent = findByAgentId(agentId);
        // agentSession可以记录requestId，这样的话就不需要座席端发送了
        agent.setServeRequestId(serveRequestId);
        if (serveRequestId < 0) {
            // 结束见证
            agent.setLastServeEndTime(new Date());
        }
        updateAgentSession(agent);
    }

    /**
     * 开始见证服务（更新坐席会话的状态）
     * 该方法用于响应坐席的接受呼叫请求：当坐席发送接受呼叫的请求后，该方法用于修改相应的状态
     * 详细逻辑：修改坐席状态为繁忙(通过修改serveClientId来标识当前坐席处于繁忙中)，修改请求表状态（请求表为业务数据表，此处不做修改，交给上层实现），客户离开队列
     * @param agentId 坐席id
     * @param serveRequestId 服务中的请求，不能送-1
     */
    public void startServe(String agentId, long serveRequestId) {
        if (serveRequestId < 0) {
            return;
        }
        // @todo 接受请求的日志要记录下，可以在坐席的接受接口中记录日志，还没实现
        updateAgentState(agentId, serveRequestId);
    }

    /**
     * 结束见证服务，修改坐席会话的状态
     * @param agentId 坐席id
     */
    public void endServe(String agentId) {
        updateAgentState(agentId, -1);
    }

    /**
     * 判断坐席是否可以处理当前请求（可服务坐席），这里主要是根据见证人员派单规则来过滤不能为客户服务的坐席。
     * 还有其他的过滤条件，则使用另外的方法交给上层实现（比如路由规则）
     * @param queueWitnessRequest 队列中的见证请求
     * @param agentSession 坐席会话
     * @return true表示可以服务
     */
    public boolean checkAgentCanServe(QueueWitnessRequest queueWitnessRequest, AgentSession agentSession, Date nowDate) {
        this.analysisDispatchRule.setExecRule(dispatchRule -> dispatchRule.canServe(queueWitnessRequest, agentSession, nowDate));
        return this.analysisDispatchRule.compareByRule();
        // 获取所有规则（已经按照优先级排过序了）
//        List<DispatchRule> dispatchRuleConfigEntities = agentComparator.getDispatchRuleConfigManager().getAllDispatchRuleConfigs();
//        for (DispatchRule dispatchRuleConfigEntity: dispatchRuleConfigEntities) {
//            if (dispatchRuleConfigEntity.getDispatchRuleConfigEntity().getRuleType() != RULE_TYPE_AGENT) {
//                // 非见证坐席派单规则的不做判断
//                continue;
//            }
//            if (dispatchRuleConfigEntity.getDispatchRuleConfigEntity().getState() != DISPATCH_STATE_OPEN) {
//                // 未启用的规则不做判断
//                continue;
//            }
//            AbstractDispatchRule abstractDispatchRule = dispatchRuleConfigEntity.getRule();
//            if (!(abstractDispatchRule instanceof AgentDispatchRule)) {
//                // 非见证坐席派单规则的不做判断
//                continue;
//            }
//            AgentDispatchRule agentDispatchRule = (AgentDispatchRule) abstractDispatchRule;
//            if (agentDispatchRule == null) {
//                // 未实现的规则不做判断
//                continue;
//            }
//            // 有具体的规则，则判断是否允许服务
//            boolean result = agentDispatchRule.canServe(queueWitnessRequest, agentSession, nowDate);
//            if (!result) {
//                // 如果有一个规则是不通过的，则返回不通过
//                return result;
//            }
//            // 当前规则下允许服务，则继续判断下一个规则
//        }
//        // 所有的规则都是通过的，则返回通过
//        return true;
    }

    /**
     * 从可服务的坐席中，寻找当前最合适的坐席，也就是坐席优先级的判断逻辑
     * @param queueWitnessRequest 队列中的见证请求，该请求中包含了呼叫信息、拒绝信息和请求数据，该方法可根据这些数据判断当前最合适服务该请求的坐席
     * @param canServeAndFreeNotCallAgents 可服务坐席中空闲且未呼叫的坐席
     * @return 当前最合适的坐席
     */
    public AgentSession getBestAgent(QueueWitnessRequest queueWitnessRequest, List<AgentSession> canServeAndFreeNotCallAgents) {
        if (canServeAndFreeNotCallAgents == null || canServeAndFreeNotCallAgents.isEmpty()) {
            return null;
        }
        // 这里增加指定坐席的判断，其中见证退回优先等规则，可以通过指定坐席来实现
        final String appointAgentId = queueWitnessRequest.getAppointAgentId();
        if (StringUtils.hasLength(appointAgentId)) {
            AgentSession appointAgentSession = canServeAndFreeNotCallAgents
                    .stream()
                    .filter(agentSession -> agentSession.getAgentId().equals(appointAgentId))
                    .findFirst()
                    .orElse(null);
//            if (appointAgentSession != null) {
//                return appointAgentSession;
//            }
            // @todo 如果指定坐席不在线不存在，则该笔请求不发送呼叫？
            return appointAgentSession;
        }
        // 是否要增加忽略坐席的判断？----先不加
        // @todo 这里是否要增加判断?:当前时间无法为该客户服务的坐席，要删掉

        // 这里要根据坐席优先级规则实现
        agentComparator.setQueueWitnessRequest(queueWitnessRequest);
        canServeAndFreeNotCallAgents.sort(agentComparator);
//        canServeAndFreeNotCallAgents.sort((agentSession1, agentSession2) -> {
//            // 这里增加指定坐席的判断，其中见证退回优先规则，可以通过指定坐席来实现
//            // 是否要增加忽略坐席的判断？
//            // 其他坐席派单优先级规则，可以参考见证请求优先级规则的逻辑来实现
//        });
        return canServeAndFreeNotCallAgents.get(0);

    }
}
