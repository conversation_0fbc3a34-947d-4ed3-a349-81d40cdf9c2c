package com.apexsoft.dispatcher.om;

import com.apexsoft.util.ConcurrentDateUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
public class QueueWitnessRequest implements Serializable {

    // 见证请求id
    private final long requestId;
    // 此次呼叫开始的时间
    private String callingTime;
    // 此次呼叫中的坐席id
    private String callingAgentId;
    // 坐席响应呼叫的状态，1，接受；-1；拒绝，其他值都属于未响应
    // 该状态只能被坐席和队列轮训线程修改，其他地方不能修改这里的状态
    private int callResponse;

    // 被坐席拒绝的时间
    @Getter(AccessLevel.NONE)  // 禁用 getter
    @Setter(AccessLevel.NONE)  // 禁用 setter
    private Map<String, Date> rejectionTimes = new HashMap<>();

    private Map<String, Boolean> canServeAgents = new HashMap<>();

    // 指定坐席id，为null或者空字符串，表示未指定
    private String appointAgentId;

    // 导致该请求位置不能变动的类型，暂时只有一类：1，手动调整
//    private String freezeType = null;
    // 手动置顶的操作时间.
    private Date topTime;

    // 见证请求数据（包含业务数据客户数据等）
//    private RD witnessRequest;

    private Object witnessRequest;

    public <RD> QueueWitnessRequest(long requestId, RD witnessRequest) {
        this.witnessRequest = witnessRequest;
        this.requestId = requestId;
        this.callingAgentId = "";
        this.callingTime = "";
        this.callResponse = 0;
        this.topTime = null;
    }

    @SuppressWarnings("unchecked")
    public <RD> RD getWitnessRequest() {
        return (RD) witnessRequest;
    }

    /**
     * 开始呼叫坐席
     * @param callingAgentId 被呼叫的坐席
     */
    public void startCall (String callingAgentId) {
        this.callingTime = ConcurrentDateUtil.now();
        this.callingAgentId = callingAgentId;
        this.callResponse = 0;
    }

    /**
     * 停止呼叫（主动拒绝和超时都会停止呼叫）
     */
    public void stopCall () {
        // 拒绝请求，记录拒绝的时间
        this.rejectionTimes.put(this.callingAgentId, new Date());
        this.callingTime = "";
        this.callingAgentId = "";
        this.callResponse = 0;
    }

    /**
     * 坐席响应
     * @param isAccept 是否接受呼叫请求
     */
    public void response (boolean isAccept) {
        this.callResponse = isAccept ? 1 : -1;
    }

    /**
     * 获取某个坐席的拒绝时间
     * @param agentId
     * @return
     */
    public Date getRejectTime (String agentId) {
        return this.rejectionTimes.get(agentId);
    }

    /**
     * 删除某个坐席的拒绝时间
     * @param agentId
     */
    public void removeRejectTime (String agentId) {
        this.rejectionTimes.remove(agentId);
    }

    /**
     * 记录该坐席是否允许服务本次见证请求
     * @param agentId
     * @param canServe
     */
    public void setAgentCanServe (String agentId, boolean canServe) {
        this.canServeAgents.put(agentId, canServe);
    }

    /**
     * 获取该坐席是否允许服务本次见证坐席的结果，如果之前未记录过，则返回null
     * @param agentId
     * @return
     */
    public Boolean isAgentCanServe (String agentId) {
        return this.canServeAgents.get(agentId);
    }
}
