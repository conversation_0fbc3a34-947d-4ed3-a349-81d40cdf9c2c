package com.apexsoft.dispatcher.om;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Slf4j
@Getter
public class AgentSession<AI extends Serializable> implements Serializable {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        try {
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        } catch (Exception e) {
            log.error("Failed to initialize AgentSession objectMapper: {}", e.getMessage());
            throw new RuntimeException("objectMapper initialization failed", e);
        }
    }

    // 坐席id（类似于userid，id等坐席唯一标识字段）
    private final String agentId;
    private final String socketClientSessionId;

    private final AI agentInfo;
    private final Class<AI> clazz;
    // @todo 有关坐席状态的变化，要记录好相应的日志，这个还没实现
    // 见证中的客户请求（有大于0的值表示繁忙状态）
    @Setter
    private long serveRequestId;
    // 最后一次服务结束的时间
    @Setter
    private Date lastServeEndTime;

    @JsonCreator
    public AgentSession(
            @JsonProperty("agentId") String agentId,
            @JsonProperty("socketClientSessionId") String socketClientSessionId,
            @JsonProperty("agentInfo") AI agentInfo) {
        this.agentId = agentId;
        this.socketClientSessionId = socketClientSessionId;
        this.agentInfo = agentInfo;
        this.serveRequestId = -1;
        this.clazz = (Class<AI>) agentInfo.getClass();
    }

    public AI getAgentInfo() {
        // 因为agentSession保存到redis后再取出来，泛型成员变量会被转换为map对象，所以这里要做个兼容，先判断类型
        if (this.agentInfo instanceof Map) {
            Map map = (Map) this.agentInfo;
            return objectMapper.convertValue(map, this.clazz);
        }
        return agentInfo;
    }
}
