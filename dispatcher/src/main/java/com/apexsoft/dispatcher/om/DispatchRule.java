package com.apexsoft.dispatcher.om;

import com.apexsoft.dispatcher.dispatch.rule.AbstractDispatchRule;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * ////////////////////////////////////////////////////////////////////
 * //                            _ooOoo_                             //
 * //                           o8888888o                            //
 * //                           88" . "88                            //
 * //                           (| -_- |)                            //
 * //                           O\  =  /O                            //
 * //                        ____/`---'\____                         //
 * //                      .'  \\|     |//  `.                       //
 * //                     /  \\|||  :  |||//  \                      //
 * //                    /  _||||| -:- |||||-  \                     //
 * //                    |   | \\\  -  /// |   |                     //
 * //                    | \_|  ''\---/''  |   |                     //
 * //                    \  .-\__  `-`  ___/-. /                     //
 * //                  ___`. .'  /--.--\  `. . ___                   //
 * //                ."" '<  `.___\_<|>_/___.'  >'"".                //
 * //              | | :  `- \`.;`\ _ /`;.`/ - ` : | |               //
 * //              \  \ `-.   \_ __\ /__ _/   .-` /  /               //
 * //        ========`-.____`-.___\_____/___.-`____.-'========       //
 * //                             `=---='                            //
 * //        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^      //
 * //         佛祖保佑       永无BUG        永不修改                   //
 * ////////////////////////////////////////////////////////////////////
 * Company: apexsoft
 *
 * <AUTHOR>
 * @date 2023/8/11 11:42
 */
@Getter
@Slf4j
public class DispatchRule{

    private AbstractDispatchRule rule = null;
    private DispatchRuleConfigEntity dispatchRuleConfigEntity = null;

    public DispatchRule(AbstractDispatchRule rule, DispatchRuleConfigEntity dispatchRuleConfigEntity) {
        this.dispatchRuleConfigEntity = dispatchRuleConfigEntity;
        if (rule != null) {
            this.rule = rule;
            this.rule.setDispatchRule(this);
            try {
                this.rule.init();
            } catch (Exception e) {
                log.error("rule [{}] error", rule, e);
            }
        }
    }

    public int getPriority() {
        return this.dispatchRuleConfigEntity.getPriority();
    }
}
