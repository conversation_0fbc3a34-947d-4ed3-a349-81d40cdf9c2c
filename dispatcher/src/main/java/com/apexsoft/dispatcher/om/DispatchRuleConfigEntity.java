package com.apexsoft.dispatcher.om;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ////////////////////////////////////////////////////////////////////
 * //                            _ooOoo_                             //
 * //                           o8888888o                            //
 * //                           88" . "88                            //
 * //                           (| -_- |)                            //
 * //                           O\  =  /O                            //
 * //                        ____/`---'\____                         //
 * //                      .'  \\|     |//  `.                       //
 * //                     /  \\|||  :  |||//  \                      //
 * //                    /  _||||| -:- |||||-  \                     //
 * //                    |   | \\\  -  /// |   |                     //
 * //                    | \_|  ''\---/''  |   |                     //
 * //                    \  .-\__  `-`  ___/-. /                     //
 * //                  ___`. .'  /--.--\  `. . ___                   //
 * //                ."" '<  `.___\_<|>_/___.'  >'"".                //
 * //              | | :  `- \`.;`\ _ /`;.`/ - ` : | |               //
 * //              \  \ `-.   \_ __\ /__ _/   .-` /  /               //
 * //        ========`-.____`-.___\_____/___.-`____.-'========       //
 * //                             `=---='                            //
 * //        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^      //
 * //         佛祖保佑       永无BUG        永不修改                   //
 * ////////////////////////////////////////////////////////////////////
 * Company: apexsoft
 *
 * <AUTHOR>
 * @date 2023/8/9 13:50
 */
@Data
@ApiModel(value = "派单规则对象", description = "具体的派单规则参数")
public class DispatchRuleConfigEntity implements Serializable {

    @ApiModelProperty(value = "规则代码", name = "ruleCode", required = true, example = "1001")
    private String ruleCode;

    @ApiModelProperty(value = "规则名称", name = "ruleName", example = "业务优先提交，优先派单")
    private String ruleName;

    @ApiModelProperty(value = "优先级，按数字排序，数值越小优先级越高", name = "priority", example = "2")
    private Integer priority;

    @ApiModelProperty(value = "取值方式：1|数据字典；2|个性字典；", name = "ruleDictType", example = "1")
    private Integer ruleDictType;

    @ApiModelProperty(value = "取值参数：字典的key名，或者详细的字典", name = "ruleDictKey", example = "KHXJ")
    private String ruleDictKey;

    @ApiModelProperty(value = "规则分类：1|派单任务规则；2|派单人员规则；", name = "ruleType", example = "1")
    private Integer ruleType;

    @ApiModelProperty(value = "显示控件类型", name = "htmlElementType", example = "3")
    private Integer htmlElementType;

    @ApiModelProperty(value = "状态，0:停用;1:开启", name = "state", example = "0")
    private Integer state;

    @ApiModelProperty(value = "是否开启自定义排序，0:不是;1:是", name = "customizeSortEnable", example = "0")
    private Integer customizeSortEnable;

    @ApiModelProperty(value = "是否默认规则，0:不是;1:是", name = "defaultRule", example = "1")
    private Integer defaultRule;

    @ApiModelProperty(value = "规则值", name = "ruleValue", example = "3|2|1")
    private String ruleValue;

    public DispatchRuleConfigEntity() {
    }

    public DispatchRuleConfigEntity(String ruleCode, String ruleName, Integer priority, Integer ruleDictType, String ruleDictKey, Integer ruleType, Integer htmlElementType, Integer state, Integer customizeSortEnable, Integer defaultRule, String ruleValue) {
        this.ruleCode = ruleCode;
        this.ruleName = ruleName;
        this.priority = priority;
        this.ruleDictType = ruleDictType;
        this.ruleDictKey = ruleDictKey;
        this.ruleType = ruleType;
        this.htmlElementType = htmlElementType;
        this.state = state;
        this.customizeSortEnable = customizeSortEnable;
        this.defaultRule = defaultRule;
        this.ruleValue = ruleValue;
    }
}
