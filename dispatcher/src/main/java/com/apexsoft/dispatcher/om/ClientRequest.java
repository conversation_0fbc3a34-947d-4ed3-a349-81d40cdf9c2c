package com.apexsoft.dispatcher.om;

import lombok.Getter;

import java.io.Serializable;

@Getter
public class ClientRequest implements Serializable {

    public enum RequestType {
        ADD, // 新增请求
        REMOVE, // 删除请求
    }

    private final RequestType requestType;
    private QueueWitnessRequest queueWitnessRequest;
    private long requestId;

    /**
     * 新增请求
     * @param queueWitnessRequest
     */
    public ClientRequest(QueueWitnessRequest queueWitnessRequest) {
        this.requestType = RequestType.ADD;
        this.queueWitnessRequest = queueWitnessRequest;
    }

    /**
     * 删除请求
     * @param requestId
     */
    public ClientRequest(long requestId) {
        this.requestType = RequestType.REMOVE;
        this.requestId = requestId;
    }

//    public ClientRequest(RequestType requestType, QueueWitnessRequest queueWitnessRequest) {
//        this.requestType = requestType;
//        this.queueWitnessRequest = queueWitnessRequest;
////        this.requestId = requestId;
//    }
}
