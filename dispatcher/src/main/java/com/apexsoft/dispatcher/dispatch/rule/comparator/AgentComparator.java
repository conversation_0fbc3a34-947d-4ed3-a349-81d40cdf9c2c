package com.apexsoft.dispatcher.dispatch.rule.comparator;

import com.apexsoft.dispatcher.config.Constant;
import com.apexsoft.dispatcher.dispatch.DispatchRuleConfigManager;
import com.apexsoft.dispatcher.dispatch.rule.AbstractDispatchRule;
import com.apexsoft.dispatcher.dispatch.rule.AgentDispatchRule;
import com.apexsoft.dispatcher.dispatch.rule.RequestDispatchRule;
import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.DispatchRule;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;

@Component
@Slf4j
public class AgentComparator implements Comparator<AgentSession>, Constant {

    @Setter
    private QueueWitnessRequest queueWitnessRequest;

    @Getter
    @Autowired
    private DispatchRuleConfigManager dispatchRuleConfigManager;

    private AnalysisDispatchRule<AgentDispatchRule, Integer> analysisDispatchRule;

    @PostConstruct
    public void init () {
        this.analysisDispatchRule = new AnalysisDispatchRule<>();
        this.analysisDispatchRule.setRuleType(Constant.RULE_TYPE_AGENT);
        this.analysisDispatchRule.setDispatchRuleClass(AgentDispatchRule.class);
        this.analysisDispatchRule.setDispatchRuleConfigManager(dispatchRuleConfigManager);
        this.analysisDispatchRule.setDefaultResult(0);
        this.analysisDispatchRule.setReturnResult(result -> result != 0);
    }

//    public AgentComparator(DispatchRuleConfigManager dispatchRuleConfigManager) {
//        this.dispatchRuleConfigManager = dispatchRuleConfigManager;
//    }

    @Override
    public int compare(AgentSession agentSession1, AgentSession agentSession2) {
//        if (agentSession1.getAgentId().equals(queueWitnessRequest.getAppointAgentId())) {
//            return -FISRT_ELE_TO_BEHIND;
//        }
//        if (agentSession2.getAgentId().equals(queueWitnessRequest.getAppointAgentId())) {
//            return FISRT_ELE_TO_BEHIND;
//        }

        this.analysisDispatchRule.setExecRule(dispatchRule -> dispatchRule.sortCompare(agentSession1, agentSession2, queueWitnessRequest));
        return this.analysisDispatchRule.compareByRule();
        // 获取所有排序规则（已经按照优先级排过序了）
//        List<DispatchRule> dispatchRuleConfigEntities = dispatchRuleConfigManager.getAllDispatchRuleConfigs();
//        // 比较入列请求和当前请求
//        for (DispatchRule dispatchRuleConfigEntity: dispatchRuleConfigEntities) {
//            if (dispatchRuleConfigEntity.getDispatchRuleConfigEntity().getRuleType() != RULE_TYPE_AGENT) {
//                // 非见证坐席派单规则的不做判断
//                continue;
//            }
//            if (dispatchRuleConfigEntity.getDispatchRuleConfigEntity().getState() != DISPATCH_STATE_OPEN) {
//                // 未启用的规则不做判断
//                continue;
//            }
//            AbstractDispatchRule abstractDispatchRule = dispatchRuleConfigEntity.getRule();
//            if (!(abstractDispatchRule instanceof AgentDispatchRule)) {
//                // 非见证坐席派单规则的不做判断
//                continue;
//            }
//            AgentDispatchRule agentDispatchRule = (AgentDispatchRule) abstractDispatchRule;
//            if (agentDispatchRule == null) {
//                // 未实现的规则不做判断
//                continue;
//            }
//            // 有具体的排序规则，则进行排序比较
//            int result = agentDispatchRule.sortCompare(agentSession1, agentSession2, queueWitnessRequest);
//            if (result != 0) {
//                return result;
//            }
//            // result == 0表示当前规则下两个请求平级，则继续判断下一个优先级
//        }
//        return 0;
    }
}
