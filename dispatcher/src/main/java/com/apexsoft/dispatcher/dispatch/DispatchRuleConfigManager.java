package com.apexsoft.dispatcher.dispatch;

import com.apexsoft.dispatcher.dispatch.rule.DispatchRuleImplFactory;
import com.apexsoft.dispatcher.dispatch.rule.DispatcherMethod;
import com.apexsoft.dispatcher.om.DispatchRule;
import com.apexsoft.dispatcher.om.DispatchRuleConfigEntity;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Component
@Slf4j
public class DispatchRuleConfigManager {

    @Getter
    private List<DispatchRule> allDispatchRuleConfigs = new ArrayList<>();

    @Autowired
    private DispatcherMethod dispatcherMethod;

    @Autowired
    private DispatchRuleImplFactory dispatchRuleImplFactory;

    @PostConstruct
    private void init() {
        try {
            dispatchRuleImplFactory.init();
            List<DispatchRuleConfigEntity> dispatchRuleConfigEntityList = dispatcherMethod.getAll();
            allDispatchRuleConfigs = new ArrayList<>();
            for (DispatchRuleConfigEntity dispatchRuleConfigEntity : dispatchRuleConfigEntityList) {
                DispatchRule dispatchRule = new DispatchRule(dispatchRuleImplFactory.getDispatchRule(dispatchRuleConfigEntity.getRuleCode()), dispatchRuleConfigEntity);
                allDispatchRuleConfigs.add(dispatchRule);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            // 按照优先级排序派单规则
            allDispatchRuleConfigs.sort(Comparator.comparingInt(DispatchRule::getPriority));
            allDispatchRuleConfigs.forEach(value ->
                    log.info("dispatchRule {} has load: {}",
                            value.getDispatchRuleConfigEntity().getRuleCode(),
                            value.getRule()
                    )
            );
        }
    }
}
