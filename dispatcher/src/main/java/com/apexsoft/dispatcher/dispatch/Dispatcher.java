package com.apexsoft.dispatcher.dispatch;

import com.apexsoft.dispatcher.AgentSessionService;
import com.apexsoft.dispatcher.dispatch.rule.DispatcherMethod;
import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.util.ConcurrentDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class Dispatcher {

    @Autowired
    private Queue queue;

    @Autowired
    private DispatcherMethod dispatcherMethod;

    @Autowired
    private AgentSessionService agentSessionService;

    /**
     * 轮询队列的逻辑 核心逻辑
     */
    // 每隔1秒执行一次
    @Scheduled(fixedRate = 1000)
    public void loopQueue() {
        // 处理等待队列中的客户
        List<QueueWitnessRequest> requests = queue.getRequestsSync();
        Date nowDate = new Date();
        for (QueueWitnessRequest request: requests) {
            try {
                dealCurrentRequest(request, nowDate);
            } catch (Exception e) {
                log.error("dealCurrentRequest", e);
            }
        }
    }

    private void dealCurrentRequest (QueueWitnessRequest queueWitnessRequest, Date nowDate) throws Exception {
        // 获取在线可服务坐席---- 放到redis中管理坐席的会话状态
        Collection<AgentSession> allAgentSession = agentSessionService.findAllAgentSession();
        List<AgentSession> canServeAgents = new ArrayList<>();
        for (AgentSession agentSession: allAgentSession) {
            if (checkAgentCanServe(queueWitnessRequest, agentSession, nowDate)) {
                canServeAgents.add(agentSession);
            }
        }
        if (canServeAgents.isEmpty()) {
            // 无坐席，则做额外的业务处理，并离开队列
//            request.setState(-2);
//            clientService.witnessFailed(request);
            dispatcherMethod.haveNoAgentOnline(queueWitnessRequest);
            queue.removeRequest(queueWitnessRequest);
            return;
        }
        // 判断该请求的状态
        String callAgentId = queueWitnessRequest.getCallingAgentId();
        if (StringUtils.hasLength(callAgentId)) {
            // 呼叫请求不为空，则判断该呼叫是否已经结束
            int agentResponse = queueWitnessRequest.getCallResponse();
            switch (agentResponse) {
                case -1:
                    // 拒绝
                    // 修改请求的呼叫状态，进入下一个呼叫请求判断
                    queueWitnessRequest.stopCall();
                    break;
                case 1:
                    // 接受
                    // 更改坐席状态
                    agentSessionService.startServe(queueWitnessRequest.getCallingAgentId(), queueWitnessRequest.getRequestId());
                    // 处理额外的业务数据
                    dispatcherMethod.onWitnessServeStart(queueWitnessRequest);
                    // 离开队列
                    queue.removeRequest(queueWitnessRequest);
                    return;
                default:
                    // 其他都属于未响应
                    // 判断当前呼叫请求是否超时
                    long nowTimeLong = Long.parseLong(ConcurrentDateUtil.now());
                    long callTimeLong = Long.parseLong(queueWitnessRequest.getCallingTime());
                    long timeoutLong = dispatcherMethod.getCallTimeout();
                    if (nowTimeLong > callTimeLong + timeoutLong) {
                        // 超时，修改请求的呼叫状态，进入下一个呼叫请求判断
                        queueWitnessRequest.stopCall();
                    } else {
                        // 未超时，说明呼叫等待中，继续发送消息给坐席，等待坐席响应，不修改状态，等待下一次轮训
                        dispatcherMethod.sendCallInfoToAgent(queueWitnessRequest, callAgentId);
//                        // 发送呼叫给坐席
//                        agentSessionService.startCall(callAgentId, message);
                        return;
                    }
                    break;
            }
        }
        // 呼叫请求为空，或者此次呼叫已超时，则判断下一个呼叫请求

        // 计算可服务坐席中，空闲坐席、见证中坐席的数量，
        // 空闲且未被呼叫的坐席
        List<AgentSession> freeNotCallAgents = canServeAgents.stream()
                .filter(agentWebSocketHandler ->
                        // 该坐席未在服务中，也未被呼叫----可服务坐席中，通过serveClientId是否为空判断是否空闲，通过是否有呼叫请求判断是否被呼叫
                        agentWebSocketHandler.getServeRequestId() <= 0 && queue.getRequestByCallAgentId(agentWebSocketHandler.getAgentId()) == null)
                .collect(Collectors.toList());
        if (freeNotCallAgents.isEmpty()) {
            // 可服务在线坐席中，空闲未呼叫坐席数量为0，则不修改状态，等待下一次轮训
            return;
        }
        // 开始呼叫
        AgentSession agentSession = agentSessionService.getBestAgent(queueWitnessRequest, freeNotCallAgents);
        // 如果存在最合适坐席，则修改请求的呼叫状态，进入下一个呼叫请求。否则不进行呼叫
        if (agentSession != null) {
            queueWitnessRequest.startCall(agentSession.getAgentId());
        }
    }

    private boolean checkAgentCanServe (QueueWitnessRequest queueWitnessRequest, AgentSession agentSession, Date nowDate) {
        // queueWitnessRequest可以记录该坐席是否判断过允许服务该客户，这样可以根据判断结果直接返回，不用每次都调用一次检查。这样会有一个问题，坐席的信息如果发生变化（比如技能组发生变化），那么之前判断的结果可能不能用
        Boolean canServe = queueWitnessRequest.isAgentCanServe(agentSession.getAgentId());
        if (canServe == null) {
            // 见证人员派单规则，以及上层实现的过滤规则，都要调用，暂时不关心先后顺序
            canServe = dispatcherMethod.checkAgentCanServe(queueWitnessRequest, agentSession, nowDate) &&
                    agentSessionService.checkAgentCanServe(queueWitnessRequest, agentSession, nowDate);
            queueWitnessRequest.setAgentCanServe(agentSession.getAgentId(), canServe);
        }
        return canServe;
    }
}
