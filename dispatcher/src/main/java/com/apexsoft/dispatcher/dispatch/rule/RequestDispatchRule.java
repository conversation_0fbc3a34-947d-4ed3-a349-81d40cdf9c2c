package com.apexsoft.dispatcher.dispatch.rule;

import com.apexsoft.dispatcher.om.QueueWitnessRequest;

import java.io.Serializable;
import java.util.List;

/**
 * ////////////////////////////////////////////////////////////////////
 * //                            _ooOoo_                             //
 * //                           o8888888o                            //
 * //                           88" . "88                            //
 * //                           (| -_- |)                            //
 * //                           O\  =  /O                            //
 * //                        ____/`---'\____                         //
 * //                      .'  \\|     |//  `.                       //
 * //                     /  \\|||  :  |||//  \                      //
 * //                    /  _||||| -:- |||||-  \                     //
 * //                    |   | \\\  -  /// |   |                     //
 * //                    | \_|  ''\---/''  |   |                     //
 * //                    \  .-\__  `-`  ___/-. /                     //
 * //                  ___`. .'  /--.--\  `. . ___                   //
 * //                ."" '<  `.___\_<|>_/___.'  >'"".                //
 * //              | | :  `- \`.;`\ _ /`;.`/ - ` : | |               //
 * //              \  \ `-.   \_ __\ /__ _/   .-` /  /               //
 * //        ========`-.____`-.___\_____/___.-`____.-'========       //
 * //                             `=---='                            //
 * //        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^      //
 * //         佛祖保佑       永无BUG        永不修改                   //
 * ////////////////////////////////////////////////////////////////////
 * Company: apexsoft
 *
 * 派单请求优先级规则抽象类
 * <AUTHOR>
 * @date 2023/8/8 11:01
 */
public abstract class RequestDispatchRule extends AbstractDispatchRule implements Serializable {

    /**
     * 优先级比较规则
     * @param o1
     * @param o2
     * @param requests
     * @return
     */
    public abstract int pushCompare(QueueWitnessRequest o1, QueueWitnessRequest o2, List<QueueWitnessRequest> requests);

    protected int commonCompare (String ruleValue1, String ruleValue2) {
//        initDispatchRule();
//        String ruleValue = dispatchRule.getDispatchRuleConfigEntity().getRuleValue();
//        String[] ruleValues = ruleValue.split("\\|");
//        int index1 = -1, index2 = -1, index = 0;
//        String ruleValue1Check = ";" + ruleValue1 + ";";
//        String ruleValue2Check = ";" + ruleValue2 + ";";
//        for (String value: ruleValues) {
//            String valueCheck = ";" + value + ";";
//            if (valueCheck.contains(ruleValue1Check)) {
//                index1 = index;
//            }
//            if (valueCheck.contains(ruleValue2Check)) {
//                index2 = index;
//            }
//            if (index1 > -1 && index2 > -1) {
//                break;
//            }
//            index++;
//        }
//        return index1 - index2;
        return ruleValueMatch(ruleValue1) - ruleValueMatch(ruleValue2);
    }
}
