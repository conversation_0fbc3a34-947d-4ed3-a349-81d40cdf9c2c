package com.apexsoft.dispatcher.dispatch.rule.comparator;

import com.apexsoft.dispatcher.config.Constant;
import com.apexsoft.dispatcher.dispatch.DispatchRuleConfigManager;
import com.apexsoft.dispatcher.dispatch.rule.AbstractDispatchRule;
import com.apexsoft.dispatcher.om.DispatchRule;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Function;

@Slf4j
@Setter
public class AnalysisDispatchRule<DR extends AbstractDispatchRule, RESULT> implements Constant {

    private DispatchRuleConfigManager dispatchRuleConfigManager;
    private int ruleType;
    private Class<DR> dispatchRuleClass;
    private RESULT defaultResult;
    private Function<DR, RESULT> ExecRule;
    private Function<RESULT, Boolean> ReturnResult;

    public RESULT compareByRule () {
        // 获取所有排序规则（已经按照优先级排过序了）
        List<DispatchRule> dispatchRuleConfigEntities = dispatchRuleConfigManager.getAllDispatchRuleConfigs();
        // 比较入列请求和当前请求
        for (DispatchRule dispatchRuleConfigEntity: dispatchRuleConfigEntities) {
            // RULE_TYPE_AGENT RULE_TYPE_REQUEST
            if (dispatchRuleConfigEntity.getDispatchRuleConfigEntity().getRuleType() != ruleType) {
                // 非指定类型（见证坐席和见证请求）的派单规则的不做判断
                continue;
            }
            if (dispatchRuleConfigEntity.getDispatchRuleConfigEntity().getState() != DISPATCH_STATE_OPEN) {
                // 未启用的规则不做判断
                continue;
            }
            AbstractDispatchRule abstractDispatchRule = dispatchRuleConfigEntity.getRule();
            if (abstractDispatchRule == null) {
                // 未实现的规则不做判断
                continue;
            }
//            if (!(abstractDispatchRule instanceof AgentDispatchRule)) {
            if (!dispatchRuleClass.isInstance(abstractDispatchRule)) {
                // 非指定类型（见证坐席和见证请求）的派单规则的不做判断
                continue;
            }
            DR dispatchRule = (DR) abstractDispatchRule;

            RESULT result = ExecRule.apply(dispatchRule);
            if (ReturnResult.apply(result)) {
                log.info("result[{}] by rule[{}]", result, dispatchRuleConfigEntity.getDispatchRuleConfigEntity());
                return result;
            }
        }
        log.info("to default result[{}]]", defaultResult);
        return defaultResult;
    }

}
