package com.apexsoft.dispatcher.dispatch.rule;

import com.apexsoft.dispatcher.config.Constant;
import com.apexsoft.dispatcher.om.DispatchRule;
import com.apexsoft.dispatcher.om.DispatchRuleDict;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 优先级规则抽象类
 */
@Setter
@Slf4j
public abstract class AbstractDispatchRule implements Serializable, Constant {

    protected Map<String, DispatchRuleDict> allRuleDict = new HashMap<>();

    /**
     * 排队规则详细配置
     */
    protected DispatchRule dispatchRule = null;

    /**
     * 实现类初始化
     */
    public void init() throws Exception {
        Integer dictType = dispatchRule.getDispatchRuleConfigEntity().getRuleDictType();
        if (dictType != null && dictType == 2) {
            allRuleDict = new HashMap<>();
            String ruleDict = dispatchRule.getDispatchRuleConfigEntity().getRuleDictKey();
            String[] dictAry = ruleDict.split(";");
            for (String dictEle: dictAry) {
                String[] dictDetails = dictEle.split("\\|");
                if (dictDetails.length >= 3) {
                    DispatchRuleDict dispatchRuleDict = new DispatchRuleDict(
                            dictDetails[0],
                            dictDetails[1],
                            dictDetails[2]
                    );

                    allRuleDict.put(dispatchRuleDict.getKey(), dispatchRuleDict);
                }
            }
            log.info("allRuleDict init {}", allRuleDict);
        }
    }

    protected int ruleValueMatch (String value) {
        return ruleValueMatchAbstract(value);
    }

    protected int ruleValueMatch (int value) {
        return ruleValueMatchAbstract(value);
    }

    /**
     * 获取目标值在规则值中的位置，如果规则值中不存在该目标值，则返回-1
     * @param value
     * @return
     */
    private <T> int ruleValueMatchAbstract (T value) {
        String ruleValue = dispatchRule.getDispatchRuleConfigEntity().getRuleValue();
        String[] ruleValues = ruleValue.split("\\|");
        String valueCheck = ";" + value + ";";
        int index = 0;
        for (String ruleValueEle: ruleValues) {
            String ruleValueCheck = ";" + ruleValueEle + ";";
            if (ruleValueCheck.contains(valueCheck)) {
                return index;
            }
            index++;
        }
        return -1;
    }

//    public abstract String ruleCode (); // 与数据库表Fpdispatchrulecode的ruleCode一致
}
