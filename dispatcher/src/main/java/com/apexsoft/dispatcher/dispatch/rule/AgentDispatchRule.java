package com.apexsoft.dispatcher.dispatch.rule;

import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;

import java.io.Serializable;
import java.util.Date;

/**
 * 坐席优先级规则抽象类
 */
public abstract class AgentDispatchRule<SI extends Serializable> extends AbstractDispatchRule implements Serializable {

    /**
     * 判断该坐席是否允许服务该客户，这里主要是根据见证人员派单规则来过滤不能为客户服务的坐席。
     * @param queueWitnessRequest 当前处理的见证请求
     * @param agentSession 当前坐席
     * @return 是否允许服务，默认允许
     */
    public boolean canServe (
            QueueWitnessRequest queueWitnessRequest,
            AgentSession<SI> agentSession,
            Date nowDate
    ) {
        return true;
    }

    /**
     * 优先级比较规则
     * @param agentSession1 坐席1
     * @param agentSession2 坐席2
     * @param queueWitnessRequest 当前处理的见证请求
     * @return
     */
    public abstract int sortCompare (
            AgentSession<SI> agentSession1,
            AgentSession<SI> agentSession2,
            QueueWitnessRequest queueWitnessRequest
    );
}
