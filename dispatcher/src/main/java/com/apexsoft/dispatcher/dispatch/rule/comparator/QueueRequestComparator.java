package com.apexsoft.dispatcher.dispatch.rule.comparator;

import com.apexsoft.dispatcher.config.Constant;
import com.apexsoft.dispatcher.dispatch.DispatchRuleConfigManager;
import com.apexsoft.dispatcher.dispatch.rule.AbstractDispatchRule;
import com.apexsoft.dispatcher.dispatch.rule.RequestDispatchRule;
import com.apexsoft.dispatcher.om.DispatchRule;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@Component
@Slf4j
public class QueueRequestComparator implements Comparator<QueueWitnessRequest>, Constant {

    @Setter
    private CopyOnWriteArrayList<QueueWitnessRequest> requests;

//    private final DispatchRuleConfigManager dispatchRuleConfigManager;

    @Autowired
    private DispatchRuleConfigManager dispatchRuleConfigManager;

    private AnalysisDispatchRule<RequestDispatchRule, Integer> analysisDispatchRule;

    @PostConstruct
    public void init () {
//        this.requests = requests;
//        this.dispatchRuleConfigManager = dispatchRuleConfigManager;
        this.analysisDispatchRule = new AnalysisDispatchRule<>();
        this.analysisDispatchRule.setRuleType(RULE_TYPE_REQUEST);
        this.analysisDispatchRule.setDispatchRuleClass(RequestDispatchRule.class);
        this.analysisDispatchRule.setDispatchRuleConfigManager(dispatchRuleConfigManager);
        this.analysisDispatchRule.setDefaultResult(0);
        this.analysisDispatchRule.setReturnResult(result -> result != 0);
    }

    @Override
    public int compare(QueueWitnessRequest request1, QueueWitnessRequest request2) {
        // @todo 手动调整顺序和前面几个位置固定不变的客户，要注意不能改变他们的当前位置，这个还没实现----不在这里实现了，在排序之前先把不能动的客户提取出来，先只支持置顶
        this.analysisDispatchRule.setExecRule(dispatchRule -> {
//            boolean r1Freeze = StringUtils.hasLength(request1.getFreezeType());
//            boolean r2Freeze = StringUtils.hasLength(request2.getFreezeType());
            // 置顶的客户，说明队列前面几个客户肯定都是不能动的，不会出现中间客户不能动的情况
//            if (r1Freeze) {
//                if (r2Freeze) {
//                    return 0;
//                }
//                return -FISRT_ELE_TO_BEHIND;
//            }
            Date topTime1 = request1.getTopTime();
            Date topTime2 = request2.getTopTime();

            // 目前只判断置顶的客户不调整位置，手动调整到其他位置的客户，后面还会被规则重新排序
            // 置顶的客户，优先级最高
            if (topTime1 != null) {
                if (topTime2 != null) {
                    return Math.toIntExact(topTime2.getTime() - topTime1.getTime());
                } else {
                    return -FISRT_ELE_TO_BEHIND;
                }
            } else {
                if (topTime2 != null) {
                    return FISRT_ELE_TO_BEHIND;
                }
            }
            return dispatchRule.pushCompare(request1, request2, requests);
        });
        return this.analysisDispatchRule.compareByRule();
        // 获取所有排序规则（已经按照优先级排过序了）
//        List<DispatchRule> dispatchRuleConfigEntities = dispatchRuleConfigManager.getAllDispatchRuleConfigs();
//        // 比较入列请求和当前请求
//        for (DispatchRule dispatchRuleConfigEntity: dispatchRuleConfigEntities) {
//            if (dispatchRuleConfigEntity.getDispatchRuleConfigEntity().getRuleType() != RULE_TYPE_REQUEST) {
//                // 非见证任务派单规则的不做判断
//                continue;
//            }
//            if (dispatchRuleConfigEntity.getDispatchRuleConfigEntity().getState() != DISPATCH_STATE_OPEN) {
//                // 未启用的规则不做判断
//                continue;
//            }
//            AbstractDispatchRule abstractDispatchRule = dispatchRuleConfigEntity.getRule();
//            if (!(abstractDispatchRule instanceof RequestDispatchRule)) {
//                // 非见证任务派单规则的不做判断
//                continue;
//            }
//            RequestDispatchRule requestDispatchRule = (RequestDispatchRule) abstractDispatchRule;
//            if (requestDispatchRule == null) {
//                // 未实现的规则不做判断
//                continue;
//            }
//            // 有具体的排序规则，则进行排序比较
//            int result = requestDispatchRule.pushCompare(request1, request2, requests);
//            if (result != 0) {
//                return result;
//            }
//            // result == 0表示当前规则下两个请求平级，则继续判断下一个优先级
//        }
//        return 0;
    }
}
