package com.apexsoft.dispatcher.dispatch.rule;

//import com.apexsoft.live.utils.SpringBeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 所有派单规则实现类工厂
 */
@Getter
@Component
@Slf4j
public class DispatchRuleImplFactory {

    private Map<String, AbstractDispatchRule> allDispatchRule = new HashMap<>();

    public AbstractDispatchRule getDispatchRule(String ruleCode) {
        return allDispatchRule.get(ruleCode);
    }

    public void init () {
        // 加载所有已实现的规则
        log.info("dispatch rule implement is loading......");
        allDispatchRule = SpringUtil.getApplicationContext().getBeansOfType(AbstractDispatchRule.class);
        log.info("dispatch rule implement load over......");
    }
}
