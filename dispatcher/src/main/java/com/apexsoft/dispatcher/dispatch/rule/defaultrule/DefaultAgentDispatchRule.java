package com.apexsoft.dispatcher.dispatch.rule.defaultrule;

import com.apexsoft.dispatcher.dispatch.rule.AgentDispatchRule;
import com.apexsoft.dispatcher.dispatch.rule.DispatcherMethod;
import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 坐席优先级默认规则
 */
@Component("2002")
@Slf4j
public class DefaultAgentDispatchRule extends AgentDispatchRule {

    @Autowired
    private DispatcherMethod dispatcherMethod;

//    @Override
//    public boolean canServe(QueueWitnessRequest queueWitnessRequest, AgentSession agentSession, Date nowDate) {
//        // 拒绝客户的坐席判断，如果拒绝时间还没到超时时间，是不能服务该客户的
//        // @todo 如果是指定坐席，是否还需要判断拒绝时间？
//        return !isReject(nowDate, agentSession, queueWitnessRequest);
//    }

    @Override
    public int sortCompare(AgentSession agentSession1, AgentSession agentSession2, QueueWitnessRequest queueWitnessRequest) {
        Date nowTime = new Date();
        boolean agent1IsReject = isReject(nowTime, agentSession1, queueWitnessRequest);
        boolean agent2IsReject = isReject(nowTime, agentSession2, queueWitnessRequest);
        if (agent1IsReject && !agent2IsReject) {
            return FISRT_ELE_TO_BEHIND;
        }
        if (!agent1IsReject && agent2IsReject) {
            return -FISRT_ELE_TO_BEHIND;
        }
        // 如果都拒绝过或者都没拒绝过，则判断最后一次服务的时间
        if (agentSession1.getLastServeEndTime() == null) {
            if (agentSession2.getLastServeEndTime() == null) {
                return 0;
            } else {
                return -FISRT_ELE_TO_BEHIND;
            }
        } else {
            if (agentSession2.getLastServeEndTime() == null) {
                return FISRT_ELE_TO_BEHIND;
            }
        }
        long time1 = agentSession1.getLastServeEndTime().getTime();
        long time2 = agentSession2.getLastServeEndTime().getTime();
        // 表示item2排在后面
        return Long.compare(time2, time1);
    }

    private boolean isReject (Date nowTime, AgentSession agentSession, QueueWitnessRequest queueWitnessRequest) {
        Date rejectTime = queueWitnessRequest.getRejectTime(agentSession.getAgentId());
        if (rejectTime == null) {
            // 没有拒绝的时间，不是拒绝的状态
            return false;
        }
        long diffInSeconds = (nowTime.getTime() - rejectTime.getTime()) / 1000;
        if (diffInSeconds > dispatcherMethod.getRejectTimeout()) {
            // 拒绝时间已超时，不再是拒绝的状态
            queueWitnessRequest.removeRejectTime(agentSession.getAgentId());
            return false;
        }
        return true;
    }
}
