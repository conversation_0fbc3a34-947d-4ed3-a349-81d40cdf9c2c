package com.apexsoft.dispatcher.dispatch.rule;

import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.DispatchRuleConfigEntity;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public interface DispatcherMethod<AI extends Serializable> {

    /**
     * 获取所有派单规则
     * @return
     * @throws Exception
     */
    List<DispatchRuleConfigEntity> getAll() throws Exception;
    /**
     * 呼叫超时时间
     * @return 呼叫超时时间，单位秒
     */
    int getCallTimeout ();

    /**
     * 拒绝超时时间，坐席拒绝后多久才能继续被该客户呼叫
     * @return 拒绝超时时间，单位秒
     */
    int getRejectTimeout ();

    /**
     * 判断坐席是否可以处理当前请求（可服务坐席），可以提供给上层来实现
     * 该方法可作为请求的路由规则，可不判断坐席是否离线
     * @param queueWitnessRequest 队列中的见证请求
     * @param agentSession 坐席会话
     * @return true表示可以处理
     */
    boolean checkAgentCanServe(QueueWitnessRequest queueWitnessRequest, AgentSession<AI> agentSession, Date nowDate);

//    /**
//     * 从可服务的坐席中，寻找当前最合适的坐席，也就是坐席优先级的判断逻辑
//     * @param queueWitnessRequest 队列中的见证请求，该请求中包含了呼叫信息、拒绝信息和请求数据，该方法可根据这些数据判断当前最合适服务该请求的坐席
//     * @param canServeAndFreeNotCallAgents 可服务坐席中空闲且未呼叫的坐席
//     * @return 当前最合适的坐席
//     */
//    default AgentSession getBestAgent(RD queueWitnessRequest, List<AgentSession> canServeAndFreeNotCallAgents) {
//        if (canServeAndFreeNotCallAgents == null) {
//            return null;
//        }
////        canServeAndFreeNotCallAgents.sort((agentSession1, agentSession2) -> {
////            // 这里增加指定坐席的判断，其中见证退回优先规则，可以通过指定坐席来实现
////            // 是否要增加忽略坐席的判断？
////            // 其他坐席派单优先级规则，可以参考见证请求优先级规则的逻辑来实现
////        });
//        return canServeAndFreeNotCallAgents.get(0);
//
//    }

    /**
     * 队列人数发生变化时触发
     * @param count
     */
    void onQueueCountChanged (int count);
    /**
     * 轮询队列后发现没有可服务的在线坐席，需要做的一些业务上的处理
     * @param queueWitnessRequest 队列中的见证请求
     */
    void haveNoAgentOnline(QueueWitnessRequest queueWitnessRequest);

    /**
     * 轮询队列发现需要发送呼叫信息给坐席时，需要给坐席发送呼叫信息，可以根据需要送客户业务数据给坐席
     * @param queueWitnessRequest 队列中的见证请求
     * @param callAgentId 目标坐席id
     */
    void sendCallInfoToAgent(QueueWitnessRequest queueWitnessRequest, String callAgentId);

    /**
     * 当坐席接受请求后，除了基本的队列状态变更外，还需要额外处理的业务数据，可在该方法中实现
     * @param queueWitnessRequest 队列中的见证请求
     */
    void onWitnessServeStart(QueueWitnessRequest queueWitnessRequest);

    /**
     * 当坐席结束见证服务后，除了修改坐席状态外，还需要额外处理的业务数据，可在此方法中实现
     * @param agentSession
     * @param result
     * @param reason
     */
//    void onWitnessServeEnd(AgentSession agentSession, String result, String reason);
}
