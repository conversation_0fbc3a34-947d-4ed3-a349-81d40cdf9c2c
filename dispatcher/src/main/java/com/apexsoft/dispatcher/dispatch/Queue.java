package com.apexsoft.dispatcher.dispatch;

import com.apexsoft.dispatcher.dispatch.rule.DispatcherMethod;
import com.apexsoft.dispatcher.dispatch.rule.comparator.QueueRequestComparator;
import com.apexsoft.dispatcher.om.ClientRequest;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CopyOnWriteArrayList;

@Service
@Slf4j
public class Queue {

//    @Autowired
//    private DispatchRuleConfigManager dispatchRuleConfigManager;

    // 已经完成优先级排序的等待队列（最终的见证请求队列），可用于轮训线程，也可用于监控队列数据时使用
    @Getter
    private List<QueueWitnessRequest> sortedRequests = new CopyOnWriteArrayList<>();
    // 是否需要重新排序，当新增见证请求、手动调整见证请求在队列中的位置时，该参数为true，排序结束后改为false
    @Getter
    private boolean needReSort = false;

    // 客户请求队列（类似消息队列），请求元素包括请求类型（新增、删除）、请求id、请求数据。该列表先进先出
    // 该请求放在队列轮训线程中，先处理好客户请求（将新增和删除操作落地执行），然后再处理队列中的数据，这样就避免了多线程的问题
    private final java.util.Queue<ClientRequest> clientRequests = new ConcurrentLinkedQueue<>();

    // 待优先级排序的等待队列（最初的见证请求队列）
    private final CopyOnWriteArrayList<QueueWitnessRequest> requests = new CopyOnWriteArrayList<>();

    @Autowired
    private QueueRequestComparator queueRequestComparator;

    @Autowired
    private DispatcherMethod dispatcherMethod;

    public void addRequest(QueueWitnessRequest request) {
        clientRequests.offer(new ClientRequest(request));
    }

    public void removeRequest(final long requestId) {
        clientRequests.offer(new ClientRequest(requestId));
    }

    /**
     * 处理客户请求列表中的客户请求
     * @return true表示此次有新增的客户，其他操作类型的客户都返回false（包括删除）
     */
    private void loopClientRequests () {
        boolean hasNewClient = false;
        // 一次处理20个请求
        int count = 20;
        while (!clientRequests.isEmpty() && count > 0) {
            try {
                ClientRequest clientRequest = clientRequests.poll();
                if (clientRequest != null && dealClientRequest(clientRequest)) {
                    hasNewClient = true;
                    needReSort = true;
                }
//                if (clientRequests.isEmpty()) {
//                    break;
//                }
            } catch (Exception e) {
                log.error("loopClientRequests [{}] error", count, e);
            }
            count--;
        }
//        return hasNewClient;
    }

    /**
     * 处理单个客户请求
     * @param clientRequest 客户请求
     * @return true表示此次为新增见证请求
     */
    private boolean dealClientRequest (ClientRequest clientRequest) {
        try {
            if (clientRequest != null) {
                switch (clientRequest.getRequestType()) {
                    case ADD:
                        this.requests.add(clientRequest.getQueueWitnessRequest());
                        return true;
                    case REMOVE:
                        this.requests.removeIf(r -> Objects.equals(r.getRequestId(), clientRequest.getRequestId()));
                        return false;
                }
            }
        } catch (Exception e) {
            log.error("dealClientRequest error", e);
        }
        return false;
    }

    // 该方法确保只在队列线程中执行，这样就没有多线程的问题了
    // 该方法是否可以不要，直接用removeRequest(final long requestId)来代替？
    void removeRequest(QueueWitnessRequest request) {
        try {
            this.requests.remove(request);
        } catch (Exception e) {
            log.error("removeRequest error", e);
        }
    }

    // 该方法不能放在多线程里，目前只能放在队列线程中使用，队列线程只有一个
    List<QueueWitnessRequest> getRequestsSync () {
        // 处理客户请求，将客户请求加入等待队列
        loopClientRequests();
        if (needReSort) {
            // 如果此次有新的客户请求进入队列，或手动调整了队列顺序，则进行重排序，否则不进行重排序
            // 根据优先级调整等待队列中的顺序
            queueRequestComparator.setRequests(requests);
            requests.sort(queueRequestComparator);

        }

        // 再返回等待队列中的客户进行处理
        if (sortedRequests.size() != requests.size()) {
            // 队列人数发生变化，则通知上层
            dispatcherMethod.onQueueCountChanged(requests.size());
        }
        sortedRequests = new ArrayList<>(requests);
        needReSort = false;
        return sortedRequests;
    }

    public QueueWitnessRequest getRequestByCallAgentId (final String callAgentId) {
        // 呼叫当前坐席的请求
        return requests.stream()
                .filter(request1 -> request1.getCallingAgentId().equals(callAgentId))
                .findFirst()
                .orElse(null);
    }

    public QueueWitnessRequest getRequestById (final Long requestId) {
        return requests.stream()
                .filter(request1 -> Objects.equals(request1.getRequestId(), requestId))
                .findFirst()
                .orElse(null);
    }

    public String topRequest (long requestId) {
        QueueWitnessRequest queueWitnessRequest = getRequestById(requestId);
        if (queueWitnessRequest == null) {
            return "队列中无该请求[requestId:" + requestId + "]";
        }
        queueWitnessRequest.setTopTime(new Date());
        needReSort = true;
        return "";
    }
}
