//gradle插件
plugins {
    id 'java'
    //springBoot插件
    id 'org.springframework.boot' version "${springBootVersion}"
    //spring依赖管理插件
    id 'io.spring.dependency-management' version "${springDependencyManagement}"
    id 'java-library'
}

//springBoot构建buildInfo信息
springBoot {
    buildInfo()
}
dependencyManagement {
    imports {
        //springBoot的依赖管理模板
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        //springCloud的依赖管理，需要和SpringBoot配套
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        //LiveSupport的依赖管理
        mavenBom "com.apexsoft:live-spring-boot2-bom:${liveVersion}"
    }
}
dependencies {
    //基础
    api "com.apexsoft:live-spring-boot-starter:${liveVersion}"

    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
}

configurations {
    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    all*.exclude group: "org.slf4j", module: "slf4j-log4j12"
}

test {
    useJUnitPlatform()
}
