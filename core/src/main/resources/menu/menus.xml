﻿<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <bean id="menuManager" class="com.apexsoft.gateway.base.config.menu.MenuManager">
        <property name="menus">
            <map>
                <!--<entry key="98101">
                    <bean class="com.apexsoft.gateway.base.config.menu.Menu">
                        <property name="name" value="SmartTrader系统权限申请"/>
                        <property name="businessProcess">
                            <bean class="com.apexsoft.gateway.business.smtmax.SmartTraderXtqxsqBusiness"></bean>
                        </property>
                    </bean>
                </entry>
                <entry key="70101">
                    <bean class="com.apexsoft.gateway.base.config.menu.Menu">
                        <property name="name" value="PB开户申请"/>
                        <property name="businessProcess">
                            <bean class="com.apexsoft.gateway.business.pbzhgl.PbkhsqBusiness"></bean>
                        </property>
                    </bean>
                </entry>-->
            </map>
        </property>
    </bean>
</beans>
