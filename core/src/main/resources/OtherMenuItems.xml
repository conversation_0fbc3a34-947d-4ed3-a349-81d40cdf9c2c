<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <bean id="OtherMenuItems" class="com.apexsoft.gateway.base.config.menu.OtherMenuItems">
        <property name="iconItems">
            <list>
                <bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="notifyInc"></property>
                    <property name="iconCls" value="notify"></property>
                    <property name="text">
                        <list>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_CN"></property>
                                <property name="text" value="消息通知"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="en"></property>
                                <property name="text" value="Notify"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_TW"></property>
                                <property name="text" value="消息通知"></property>
                            </bean>
                        </list>
                    </property>
                    <property name="action" value="notify"></property>
                    <property name="url" value="/livebos/NotificationMain.page"></property>
                    <property name="notification" value="NoticeNotification"></property>
                </bean>
                <bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="changeLocale"></property>
                    <property name="iconCls" value="changeLocale"></property>
                    <property name="text">
                        <list>

                        </list>
                    </property>
                    <property name="action" value="changeLocale"></property>
                    <property name="url" value=""></property>
                    <property name="notification" value=""></property>
                </bean>
                <!--<bean class="com.apexsoft.gateway.base.config.menu.MenuItem">
                    <property name="id" value="home"></property>
                    <property name="iconCls" value="home"></property>
                    <property name="text" value="首页"></property>
                    <property name="action" value=""></property>
                    <property name="url" value="/react/portal/home.page"></property>
                    <property name="notification" value=""></property>
                </bean>-->
                <!--<bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="bp"></property>
                    <property name="iconCls" value="workflow"></property>
                    <property name="text" value="流程中心"></property>
                    <property name="action" value=""></property>
                    <property name="url" value="/jgyy/child/tyzh/#/workflow"></property>
                    <property name="notification" value="TodoTaskNotification"></property>
                    <property name="func" value="WF-MyTask"></property>
                </bean>-->
            </list>
        </property>
        <property name="textItems">
            <list>
                <bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="updateUserPhoto"></property>
                    <property name="iconCls" value="user-edit"></property>
                    <property name="text">
                        <list>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_CN"></property>
                                <property name="text" value="更新头像"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="en"></property>
                                <property name="text" value="Update Your Photo"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_TW"></property>
                                <property name="text" value="更新頭像"></property>
                            </bean>
                        </list>
                    </property>
                    <property name="action" value="updateUserPhoto"></property>
                    <property name="url" value=""></property>
                    <property name="notification" value=""></property>
                </bean>
                <!--<bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="changePassword"></property>
                    <property name="iconCls" value="change-password"></property>
                    <property name="text" value="修改密码"></property>
                    <property name="action" value="doChangeMyPassword"></property>
                    <property name="url" value=""></property>
                    <property name="notification" value=""></property>
                </bean>-->
                <!--<bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="theme"></property>
                    <property name="iconCls" value="theme"></property>
                    <property name="text">
                        <list>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_CN"></property>
                                <property name="text" value="主题"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="en"></property>
                                <property name="text" value="Theme"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_TW"></property>
                                <property name="text" value="主題"></property>
                            </bean>
                        </list>
                    </property>
                    <property name="action" value="selectTheme"></property>
                    <property name="url" value=""></property>
                    <property name="notification" value=""></property>
                </bean>-->
                <bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="sendMessage"></property>
                    <property name="iconCls" value="send-message"></property>
                    <property name="text">
                        <list>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_CN"></property>
                                <property name="text" value="发送消息"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="en"></property>
                                <property name="text" value="Send Message"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_TW"></property>
                                <property name="text" value="發送消息"></property>
                            </bean>
                        </list>
                    </property>
                    <property name="action" value="sendMessage"></property>
                    <property name="url" value=""></property>
                    <property name="notification" value=""></property>
                </bean>
                <!--<bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="workSurrogate"></property>
                    <property name="iconCls" value="work-surrogate"></property>
                    <property name="text" value="流程委托"></property>
                    <property name="action" value=""></property>
                    <property name="url" value="/livebos/UIProcessor?Table=lbSurrogate"></property>
                    <property name="notification" value=""></property>
                    <property name="func" value="lbSurrogate"></property>
                </bean>-->
                <bean class="com.apexsoft.gateway.base.config.menu.OtherMenuItem">
                    <property name="id" value="logout"></property>
                    <property name="iconCls" value="logout"></property>
                    <property name="text">
                        <list>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_CN"></property>
                                <property name="text" value="注销"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="en"></property>
                                <property name="text" value="Sign out"></property>
                            </bean>
                            <bean class="com.apexsoft.gateway.base.config.menu.Menui18n">
                                <property name="lang" value="zh_TW"></property>
                                <property name="text" value="註銷"></property>
                            </bean>
                        </list>
                    </property>
                    <property name="action" value="doLogout"></property>
                    <property name="url" value=""></property>
                    <property name="notification" value=""></property>
                </bean>
            </list>
        </property>
    </bean>

</beans>