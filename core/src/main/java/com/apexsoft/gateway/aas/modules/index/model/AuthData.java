package com.apexsoft.gateway.aas.modules.index.model;

import com.alibaba.fastjson.JSONObject;

/**
 * Created by Administrator on 2018/3/13.
 */
public class AuthData {
    private String clientId;
    private String user;
    private String password;
    private String token;
    private String userId;



    private String mode;
    private long timestamp;
    private JSONObject ext;
    private boolean ignoreVerifiCode;
    private String signature;

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public JSONObject getExt() {
        return ext;
    }

    public void setExt(JSONObject ext) {
        this.ext = ext;
    }

    public boolean isIgnoreVerifiCode() {
        return ignoreVerifiCode;
    }

    public void setIgnoreVerifiCode(boolean ignoreVerifiCode) {
        this.ignoreVerifiCode = ignoreVerifiCode;
    }
    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
