package com.apexsoft.gateway.aas.common.session;

import com.apexsoft.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.gateway.base.intercept.HttpContext;
import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.live.session.UserAuthenticateSession;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.*;

@Component
public class UserSession {
    private static final Logger LOG = LoggerFactory.getLogger(UserSession.class);
    public static final String KEY_USER = "user";
    private static UserSession $this;
    @Autowired(
            required = false
    )
  /*  private IAuthUserService authUserService;
    @Autowired(
            required = false
    )
    private SessionService sessionService;*/
    @Value("${aas.session.share.enabled:false}")
    private boolean share;
    @Value("${aas.authentication.type:}")
    private String authType;
    private boolean ac = false;
    @Value("${aas.session.cas.server:}")
    private String casServer;

    public UserSession() {
    }

    @PostConstruct
    public void init() {
        $this = this;
        if ("ac".equals(this.authType)) {
            this.ac = true;
        }

    }



    public static HttpServletRequest getRequestHolder() {
        return HttpContext.getRequest();
    }





    public static String getAccessToken() {
        return HttpContext.getRequest().getSession(true).getId();
    }

    public static <T extends AuthUser> T getUserSession(String mode) {

        return UserAuthenticateSession.getUser(HttpContext.getRequest());
    }

    public static <T extends AuthUser> T getUserSession() {
        return getUserSession("user");
    }

    public static <T extends AuthUser> void setUserSession(T data) {
        setUserSession("user", data);
    }

    public static <T extends AuthUser> void setUserSession(String mode, T data) {

        UserAuthenticateSession.setUser(HttpContext.getRequest(),data);

    }

    public static void removeUserSession(String mode) {
        UserAuthenticateSession.removeUser(HttpContext.getRequest());

    }

    public static void updateUserPhoto(String id ,String photo) throws IOException {
        FileWriter writer = null;
        try{
            File file = new File(Constant.USER_PHOTO_PATH);
            if(!file.exists()){
                file.mkdirs();
            }
            File photoFile = new File(Constant.USER_PHOTO_PATH+File.separator+id);
            if(!photoFile.exists()){
                photoFile.createNewFile();
            }
            writer = new FileWriter(photoFile);
            writer.write("");//清空原文件内容
            writer.write(photo);
        }catch (Exception e){
            throw e;
        }finally {
            if(writer!=null){
                writer.flush();
                writer.close();
            }
        }

    }

    public static String getUserPhoto(String id) throws IOException {
        StringBuffer sbf = new StringBuffer();
        File photoFile = new File(Constant.USER_PHOTO_PATH+File.separator+id);
        if(photoFile.exists()){
            BufferedReader reader = new BufferedReader(new FileReader(photoFile));

            String tempStr;
            while ((tempStr = reader.readLine()) != null) {
                sbf.append(tempStr);
            }
        }
        return sbf.toString();
    }

    public static void removeUserSession() {
        removeUserSession("user");
    }

    private static void sessionCreated(String sessionId, AuthUser authUser) {
        /*TaskSchedulerUtils.execute(() -> {
            if (null != $this.sessionService) {
                $this.sessionService.sessionCreated(sessionId, authUser);
            }

        });*/
    }
}
