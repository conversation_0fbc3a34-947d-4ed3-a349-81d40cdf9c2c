package com.apexsoft.gateway.aas.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.aas.common.session.UserSession;
import com.apexsoft.gateway.aas.modules.index.model.AuthData;
import com.apexsoft.gateway.aas.modules.index.model.AuthUser;

import com.apexsoft.gateway.common.utils.*;
import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.apexsoft.gateway.dao.UserDao;
import com.apexsoft.gateway.model.JSONResponse;

import com.apexsoft.live.exception.AuthException;
import com.apexsoft.live.session.AbstractUserAuthenticate;
import com.apexsoft.live.session.UserAuthenticateContext;
import com.apexsoft.live.utils.AES;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UserAuthenticate extends AbstractUserAuthenticate {

    @Value("${application.name}")
    private String ps;

    @Autowired
    UserDao userDao;


    @Autowired
    LdapUtil ldapUtil;
    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 继承aas登录的方法
     *
     * @param userAuthenticateContext
     * @param request
     * @param response
     */
    @Override
    public void auth(UserAuthenticateContext userAuthenticateContext, HttpServletRequest request, HttpServletResponse response) throws AuthException {
        AuthUser<JSONObject> authUser = new AuthUser<JSONObject>();

        try {

            // 使用 getParameter 方法直接获取请求参数
            String username = request.getParameter("username");
            String password = request.getParameter("password");
            String captchaKey = request.getParameter("captchaKey");
            String captchaCode = request.getParameter("captchaCode");
            String signature = request.getParameter("signature");
            String ext = request.getParameter("ext");
            String mode = request.getParameter("mode");
            String token = request.getParameter("token");
            String userId = request.getParameter("userId");


            JSONObject params = new JSONObject();
            params.put("username", username);
            params.put("password", password);
            params.put("mode", mode);
            params.put("captchaKey", captchaKey);
            params.put("captchaCode", captchaCode);
            params.put("signature", signature);
            params.put("ext", ext);
            params.put("token",token);
            params.put("userId",userId);
            Map<String, Object> data = new HashMap<>();
            String ip = "";
            JSONObject userInfo = new JSONObject();




            data.put("code", JSONResponse.CODE_FAIL);
            data.put("note", "柜员登录认证成功");
            data.put("userinfo", userInfo);
            data.put("ip", ip);
            AuthData authData = null;

            authData = getAudhData(params, request);
            authUser.setAccessToken(UserSession.getAccessToken());
            authUser.setClientId(authData.getClientId());
//            String czzd = authData.getExt().getString("czzd");
            //获取登录用户IP
            ip = getIp(request);
            authUser.setIp(ip);

            if(Constant.LOGIN_MODE_CUSTOMER_SELF.equals(authData.getMode())) {//客户自助登录

                JSONObject result = userDao.verifyPassword(authData.getUser(), authData.getPassword());

                if (result.getIntValue("code")!=1) {
                    // authResponse.setNote("柜员当前状态为登录失败："+result.getString("note"));

                    throw new AuthException("当前状态为登录失败：" + "用户名或密码输入错误!");
//
                }
                JSONObject customerInfo=new JSONObject();
                userInfo.put("username", authData.getUser());
                authUser.setUser(customerInfo);
                authUser.setUserId(authData.getUser());
                userInfo.put("loginIp", ip);
                //设置会话中存储的信息
                String uid = authData.getUser();
                userAuthenticateContext.setUserinfo(authUser);
                //设置返回给前端的报文
                String customerToken=jwtUtil.createJWT(uid, "");

                data.put("code", JSONResponse.CODE_SUCCESS);
                data.put("note", "登录认证成功");
                data.put("data", customerToken);
                data.put("ip", ip);
                userAuthenticateContext.setAuthResponse(data);
                return ;
            }

            JSONObject result = new JSONObject();
            String uid = "";
            if(!Constant.LOGIN_MODE_CIF_TOKEN.equals(authData.getMode())){
                result = userDao.verifyPassword(authData.getUser(), authData.getPassword());
                if (result.getIntValue("code")!=1) {
                    // authResponse.setNote("柜员当前状态为登录失败："+result.getString("note"));
                    throw new AuthException("柜员当前状态为登录失败：" + "用户名或密码输入错误!");

                }
                uid =  authData.getUser();
            } else {
                Boolean res = verifyToken(authData.getUserId(),authData.getToken());
                if(!res){
                    throw new AuthException("柜员当前状态为登录失败：" + "token解析失败");
                }
                uid =  authData.getUserId();
            }

            // 查询用户信息
            result = userDao.queryUser(uid);


            if (result.getIntValue("code")!=1){
                throw new AuthException(result.getString("note"));
            }
            userInfo=result.getJSONArray("records").getJSONObject(0);
            authUser.setUser(userInfo);
            authUser.setUserId(authData.getUser());
            userInfo.put("loginIp", ip);
            //设置会话中存储的信息
            userAuthenticateContext.setUserinfo(authUser);


            //设置返回给前端的报文
            data.put("code", JSONResponse.CODE_SUCCESS);
            data.put("note", "柜员登录认证成功");
            data.put("data", userInfo);
            data.put("ip", ip);
            userAuthenticateContext.setAuthResponse(data);
        } catch (AuthException e){
            throw e;
        } catch (Exception e) {
            throw new AuthException("Failed to authenticate user", e);
        }
    }

    @Override
    public void handleLogoutResult(UserAuthenticateContext context, HttpServletRequest request, HttpServletResponse response) throws IOException {
        //livebos退出

        super.handleLogoutResult(context, request, response);
    }

    @Override
    public void handleNoAuthResult(HttpServletRequest request, HttpServletResponse response) throws IOException {

        response.setStatus(900);
        Map<String, Object> data = new HashMap<>();
        data.put("code", -1);
        data.put("note", "会话过期");

        //开启统一认证，需要跳转到统一认证的网址登录
        handleJSONReponse(response, data);
    }


    public static String getData(HttpServletRequest request) throws IOException {
        InputStreamReader is = null;
        try {
            is = new InputStreamReader(request.getInputStream(), request.getCharacterEncoding());
            char[] chars = new char[8 * 1024];
            int len;
            StringBuilder sb = new StringBuilder();
            while ((len = is.read(chars)) != -1) {
                sb = sb.append(chars, 0, len);
            }
            return sb.toString();

        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public Boolean verifyToken(String userId,String token) throws Exception{
        if(StringUtils.isBlank(userId) || StringUtils.isBlank(token)){
            return  false;
        }
        String encryptionToken = MD5.getVideoToken(userId);
        return token.equals(encryptionToken);
    }

    public AuthData getAudhData(JSONObject data, HttpServletRequest request) throws Exception {
        AuthData authData = new AuthData();
        try {
            authData.setExt(data.getJSONObject("ext"));
        } catch (Exception e) {
            authData.setExt(new JSONObject());
        }
        authData.setClientId(data.getString("clientId"));
        authData.setMode(data.getString("mode"));
        authData.setToken(data.getString("token"));
        authData.setUserId(data.getString("userId"));

        String signature = data.getString("signature");
        String decryptData = AES.decrypt(signature, ps);
        if(Constant.LOGIN_MODE_CIF_TOKEN.equals(data.getString("mode"))){
            return authData;
        }
        if (decryptData == null) {
            String referer = request.getHeader("Referer");
            if (StringUtils.isEmpty(referer) || !referer.endsWith("swagger-ui.html")) {
                throw new AuthException(-1, "非法请求,秘钥不合法");
            }

            log.error("加密串解码失败:" + signature);
        } else {
            try {
                JSONObject json = JSON.parseObject(decryptData);
                authData.setUser(json.getString("user"));
                authData.setPassword(json.getString("password"));
                try {
                    authData.setTimestamp(Long.valueOf(json.getString("timestamp")));
                } catch (Exception var16) {
                    log.warn("timestamp解析异常", var16);
                }
            } catch (Exception var17) {
                String[] maps = decryptData.split("&");
                String[] var9 = maps;
                int var10 = maps.length;

                for (int var11 = 0; var11 < var10; ++var11) {
                    String keyvalue = var9[var11];
                    int firstIndex = keyvalue.indexOf("=");
                    if (keyvalue.substring(0, firstIndex).equals("user")) {
                        authData.setUser(keyvalue.substring(firstIndex + 1));
                    } else if (keyvalue.substring(0, firstIndex).equals("password")) {
                        authData.setPassword(keyvalue.substring(firstIndex + 1));
                    } else if (keyvalue.substring(0, firstIndex).equals("timestamp")) {
                        try {
                            authData.setTimestamp(Long.valueOf(keyvalue.substring(firstIndex + 1)));
                        } catch (Exception var15) {
                            log.warn("timestamp解析异常", var15);
                        }
                    }
                }
            }
        }
        return authData;
    }

    @Override
    public List<String> getSessionExpiredPrelongBlackUrls() {
        ArrayList<String> list = new ArrayList<>();
        list.add("/user/getUnreadNotificationCount");
        list.add("/workflow/ywsjtj");
        return list;
    }

    @Override
    public List<String> getExcludeUrls() {
        List<String> excludeUrls = new ArrayList<>();
        excludeUrls.add("/ws/**");
        excludeUrls.add("/customer/*");
        excludeUrls.add("/ai-common/*");

        return excludeUrls;
    }

    //获取登录用户IP
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        log.info("IpTool X-Forwarded-For ip:{}", ip);
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
            log.info("IpTool X-Real-IP ip:{}", ip);
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            log.info("IpTool getRemoteAddr ip:{}", ip);
        }
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip) && StringUtils.contains(ip, ",")) {
            // 多次反向代理后会有多个IP值，第一个为真实IP。
            ip = StringUtils.substringBefore(ip, ",");
            log.info("IpTool substringBefore ip:{}", ip);
        }
        // 处理localhost访问
        if (StringUtils.isBlank(ip) || "unkown".equalsIgnoreCase(ip) || StringUtils.split(ip, ".").length != 4) {
            try {
                InetAddress inetAddress = InetAddress.getLocalHost();
                ip = inetAddress.getHostAddress();
                log.info("IpTool getLocalHost ip:{}", ip);
            } catch (UnknownHostException e) {
                e.printStackTrace();
            }
        }
        return ip;
    }


    /*public static void main(String[] args) {
        String decryptData = AES.decrypt("s7n/qycCFUcOJd97qGT8eoS/0Eymjd0tb0NcoCoCTYNLCs2ixU2CFgf7NJXKIG105UxDrOvs9RBcOnWROtd5Fpy299nxrueNJgrh/z6MoHNSQQKBVAjNSqzHsoYYFrm3CFfSxYejyixO5P8AHjVgB99kTC0ZGqvf9Ie1SOsMSGg7vV0vKLrfyX41iVRGwq0SWgebiBCweSN+pk6SaYnjvgw+iSh3sPjdR+UhXJ5iXnWNDVwtHYBdGDrhPyl5dJxhdHvoEX6zV5webGYADuHWNUZLns4YHcla89lRsJachjOlPJ0XQetRaEWnzrjuGpNnklUraimxhbfu1lEd4HJ5lw==" +
                "", "ygt_api_gatway");
        System.out.println(decryptData);

        String signData = "mode=user&clientId=jgyy_api_gateway&user=admin&password=111111&ext={\"czzd\":\"PC;IIP=NA;IPORT=NA;LIP=***********;MAC=005056B60D13;HD=NA;PCN=BJ-VDIEQ01-013;CPU=1F8BFBFF000306F0;PI=C^NTFS^99;VOL=52BE4942\"}&timestamp=1675852420696";
        String signDatamw = AES.encrypt(signData,"api_gateway");
        System.out.println(signDatamw);
    }*/
}
