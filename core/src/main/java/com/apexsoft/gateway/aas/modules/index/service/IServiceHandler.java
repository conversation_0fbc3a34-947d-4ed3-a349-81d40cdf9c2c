package com.apexsoft.gateway.aas.modules.index.service;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.model.JSONResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * Created by Administrator on 2018/7/13.
 */
public interface IServiceHandler {

    JSONObject preHandle(String func, String version, HttpServletRequest request, HttpServletResponse response) throws Exception;

    JSONResponse postHandle(JSONResponse jsonResponse, HttpServletRequest request, HttpServletResponse response);


    class Reader {
        private static Logger LOG = LoggerFactory.getLogger(Reader.class);

        public static String getData(HttpServletRequest request) throws IOException {
            InputStreamReader is = null;
            try {
                is = new InputStreamReader(request.getInputStream(), request.getCharacterEncoding());
                char[] chars = new char[8 * 1024];
                int len;
                StringBuilder sb = new StringBuilder();
                while ((len = is.read(chars)) != -1) {
                    sb = sb.append(chars, 0, len);
                }
                return sb.toString();

            } finally {
                if (is != null) {
                    try {
                        is.close();
                    } catch (IOException e) {
                        LOG.error(e.getMessage(), e);
                    }
                }
            }
        }
    }
}
