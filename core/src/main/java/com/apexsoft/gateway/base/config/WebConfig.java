package com.apexsoft.gateway.base.config;

import com.apexsoft.gateway.base.intercept.ClientIdInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

/**
 * <AUTHOR>
 * @date 2024-09-06 15:07
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    ClientIdInterceptor clientIdInterceptor;


    @Value("${application.tempDic}")
    private String tempDic;
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(clientIdInterceptor)
                .addPathPatterns("/service/**"); // 拦截所有请求

    }
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 将本地文件映射到 URL
        registry.addResourceHandler("/media/**")
                .addResourceLocations("file:"+tempDic);
    }
}
