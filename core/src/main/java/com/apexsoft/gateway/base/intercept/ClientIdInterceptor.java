package com.apexsoft.gateway.base.intercept;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.gateway.base.constant.UserEnum;
import com.apexsoft.live.session.UserAuthenticateSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-06 14:53
 */
@Component
public class ClientIdInterceptor implements HandlerInterceptor {
//    clientId为聚力的时候允许访问的接口
    private static final List<String> ALLOWED_INTERFACES_FOR_SYNERGY = Arrays.asList(
            "/service/ibos.business.IBusinessQueryService/queryBussReqElement",
            "/service/ibos.workflow.IWorkflowControllerService/claimWorkflow",
            "/service/ibos.business.IBusinessQueryService/queryBussReqData",
            "/service/ibos.business.IBusinessQueryService/getBussreqOperjour",
            "/service/ibos.workflow.IWorkflowService/getWorkAvailableAction",
            "/service/ibos.workflow.IWorkflowformService/getWorkflowFormList",
            "/service/ibos.workflow.IWorkflowControllerService/releaseClaimedWorkflow",
            "/service/ibos.workflow.IWorkflowformDefService/getWorkflowFormDefList",
            "/service/ibos.workflow.IWorkflowService/auditWorkflow",
            "/service/ibos.business.IBusinessQueryService/queryBussReqInfo",
            "/service/ibos.workflow.IWorkflowService/queryWorktasks",
            "/service/ibos.system.ISysdictService/querySysdict",
            "/service/ibos.workflow.IWorkflowService/queryWorkflow",
            "/service/ibos.system.query.IQuerySysparamService/queryParamValue",
            "/service/ibos.system.query.IQuerySysconfigService/queryConfigValue",
            "/service/ibos.system.query.IQueryOrganizationService/queryOrganization",
            "/service/ibos.system.query.IQueryUserInfoService/queryAllUserByOrgid",
            "/service/ibos.policy.IPolicyInspectService/getPolicyByOrder",
            "/service/ibos.policy.IPolicyService/queryPolicies"
    );
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        AuthUser<JSONObject> authUser = UserAuthenticateSession.getUser(request);
        if(authUser!=null){
            String clientId = authUser.getClientId();
            if(UserEnum.SYNERGY.getClientId().equals(clientId)){
                String requestURI = request.getRequestURI();
                if(!isAllowedInterface(requestURI)){
                    response.setCharacterEncoding("UTF-8");
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    response.getWriter().write("未获得访问该服务权限");
                    return false;
                }
            }
        }
        return true;
    }
    // 定义允许访问的接口列表
    private boolean isAllowedInterface(String requestUri) {
        // 这里定义允许聚力访问的接口URI
        return ALLOWED_INTERFACES_FOR_SYNERGY.contains(requestUri);
    }
}
