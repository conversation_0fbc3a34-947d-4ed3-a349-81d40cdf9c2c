package com.apexsoft.gateway.base.config;

/**
 * 常量定义类。
 *
 * <AUTHOR>
 */

import com.apexsoft.live.session.IUserAuthenticateConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

@Configuration
@Slf4j
public class AutoConfiguration {

    @Autowired(required = false)
    IUserAuthenticateConfig userAuthenticateConfig;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("recive ApplicationReadyEvent at JgyyAutoConfiguration");

        if (userAuthenticateConfig != null) {
            userAuthenticateConfig.addComponentExcludeUrls("/sso/*");
            userAuthenticateConfig.addComponentExcludeUrls("/cif/uploadByStream");
            userAuthenticateConfig.addComponentExcludeUrls("/cif/download/*");
            userAuthenticateConfig.addComponentExcludeUrls("/common/getApplicationName");
            userAuthenticateConfig.addComponentExcludeUrls("/witness/client/*");
        }

    }
}
