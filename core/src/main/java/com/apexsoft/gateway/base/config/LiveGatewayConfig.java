package com.apexsoft.gateway.base.config;

import com.apexsoft.client.*;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Auther yeyilun
 * @Date 2021 12 11
 * @Description:
 */
@Slf4j
@Configuration
public class LiveGatewayConfig {

    @Value("${esb.address:}")
    private String address;
    @Value("${esb.ip:}")
    private String ip;

    @Value("${esb.user:}")
    private String user;

    @Value("${esb.pwd:}")
    private String pwd;

    @Value("${esb.port:}")
    private String port;

    @Bean(name = "esbGateway")
    public GatewayClient gatewayClient() {
        GatewayClient client = null;
        try {
            client = new GatewayClientV1Impl();
            client.ignoreCertVerify();
            if(!StringUtils.isEmpty(address)) {
                String[] addAry=address.split(",");
                for(String add:addAry){
                    client.addHTTPSServer(add.split(":")[0],Integer.parseInt(add.split(":")[1]),null,null);
                }
            }else{
                client.addHTTPSServer(ip, Integer.parseInt(port), null, null);
            }
            client.setAuth(user, pwd);

            InvocationHandler handler = new GatewayClientProxy(client);
            Object bean = Proxy.newProxyInstance(GatewayClient.class.getClassLoader(),
                    new Class[]{GatewayClient.class},
                    handler);
            log.info("蜂巢初始化成功");
            return (GatewayClient)bean;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("蜂巢认证失败：" + e.getMessage());
            //如果初始化异常，销毁对象，回收资源
            if (client != null) {
                client.destory();
            }
        }
        return client;
    }

    class GatewayClientProxy implements InvocationHandler {

        private GatewayClient gatewayClient;
        public GatewayClientProxy(GatewayClient gatewayClient) {
            this.gatewayClient = gatewayClient;
        }
        @Override
        public Object invoke(Object o, Method method, Object[] args) throws Throwable {
            Long t = System.currentTimeMillis();
            if("service".equals(method.getName())) {
                Request req = null;
                Response res = null;
                try {
                    req = (Request)args[0];
                    Object result = method.invoke(gatewayClient, args);
                    res = (Response) result;

                    return res;
                } catch (Exception e) {

                    log.error("esb请求异常", e);
                    Response response = new Response();
                    response.put("code", -1);
                    response.put("note", "esb请求异常，" + e.getMessage());
                    return response;
                }finally {
                    log.info("调用esb服务[{}],请求参数[{}],请求结果[{}],请求耗时[{}]", req.getServiceId(), req.toJSON(),
                            res == null ? "" : res.toJSONString(),(System.currentTimeMillis() - t)+"ms");
                    try {
                        if (req.getServiceId().startsWith("esb.hs")) {
                            Pattern pattern = Pattern.compile("\\[(.*?)\\]");
                            Matcher matcher = pattern.matcher(res.getNote());
                            int i = 0;
                            String note = "";
                            boolean isMatch=false;
                            while (matcher.find()) {
                                if (i == 2 || i == 3) {
                                    note += "[" + matcher.group(1) + "]";
                                    isMatch=true;
                                }
                                i++;
                            }
                            if(isMatch)
                                res.setNote(note);
                        }
                    }catch (Exception e){
                        log.error(e.getMessage(),e);
                    }
                }
            } else {
                return method.invoke(gatewayClient, args);
            }
        }
    }
}
