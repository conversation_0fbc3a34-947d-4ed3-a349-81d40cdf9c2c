package com.apexsoft.gateway.common.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletRequest;

public class AddressUtils {
    public AddressUtils() {
    }

    public static JSONObject getAddresses() throws UnsupportedEncodingException {
        return getAddresses(getInternetIp());
    }

    public static JSONObject getAddresses(String ip) throws UnsupportedEncodingException {
        JSONObject result = new JSONObject();
        result.put("success", false);
        result.put("note", "无效IP地址");
        String urlStr = "http://ip.taobao.com/service/getIpInfo.php?ip=" + ip;
        String returnStr = GetIpDetails(urlStr, "GB2312");
        if (returnStr != null) {
            JSONObject rs = JSON.parseObject(returnStr);
            if (rs.getIntValue("code") == 1) {
                result.put("success", false);
                result.put("note", "无效IP地址");
            } else {
                rs = rs.getJSONObject("data");
                String area = decodeUnicode(rs.getString("area"));
                String area_id = decodeUnicode(rs.getString("area_id"));
                String country = decodeUnicode(rs.getString("country"));
                String country_id = decodeUnicode(rs.getString("country_id"));
                String region = decodeUnicode(rs.getString("region"));
                String region_id = decodeUnicode(rs.getString("region_id"));
                String city = decodeUnicode(rs.getString("city"));
                String city_id = decodeUnicode(rs.getString("city_id"));
                String county = decodeUnicode(rs.getString("county"));
                String county_id = decodeUnicode(rs.getString("county_id"));
                String isp = decodeUnicode(rs.getString("isp"));
                result.put("success", true);
                result.put("note", "IP地址获取成功！");
                result.put("GJ", country);
                result.put("GJ_ID", country_id);
                result.put("AREA", area);
                result.put("AREA_ID", area_id);
                result.put("PROVINCE", region);
                result.put("PROVINCE_ID", region_id);
                result.put("CITY", city);
                result.put("CITY_ID", city_id);
                result.put("SEC", county);
                result.put("SEC_ID", county_id);
                result.put("ISP", isp);
            }
        }

        return result;
    }

    public static JSONObject getPhoneInfo(String phone) throws UnsupportedEncodingException {
        String url = "http://api.showji.com/Locating/www.showji.co.m.aspx?m=" + phone + "&output=json&callback=apexsoft";
        String line = GetIpDetails(url, "UTF-8");
        String REGEX_EXP = "^apexsoft\\((.*)\\)$";
        JSONObject sjjg = new JSONObject();
        sjjg.put("success", false);
        sjjg.put("note", "手机号归属地查询失败！");
        Pattern p = Pattern.compile(REGEX_EXP);
        if (StringUtils.isNotBlank(line)) {
            line = line.replace(";", "");
            Matcher m = p.matcher(line.trim());
            if (m.matches()) {
                JSONObject tem = JSON.parseObject(m.group(1));
                if (tem != null && tem.getBooleanValue("QueryResult")) {
                    sjjg.put("success", true);
                    sjjg.put("note", "手机号归属地查询成功！");
                    sjjg.put("province", tem.getString("Province"));
                    sjjg.put("provincecode", tem.getString("AreaCode"));
                    sjjg.put("citycode", tem.getString("PostCode"));
                    sjjg.put("city", tem.getString("City"));
                    sjjg.put("corp", tem.getString("Corp"));
                }
            }
        }

        return sjjg;
    }

    public static String getIntranetIp(HttpServletRequest request) {
        String sip = request.getHeader("X-Real-IP");
        if (sip == null || sip.length() == 0 || "unknown".equalsIgnoreCase(sip)) {
            sip = request.getHeader("x-forwarded-for");
        }

        if (sip == null || sip.length() == 0 || "unknown".equalsIgnoreCase(sip)) {
            sip = request.getHeader("Proxy-Client-IP");
        }

        if (sip == null || sip.length() == 0 || "unknown".equalsIgnoreCase(sip)) {
            sip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (sip == null || sip.length() == 0 || "unknown".equalsIgnoreCase(sip)) {
            sip = request.getRemoteAddr();
        }

        return sip;
    }

    public static String getInternetIp() {
        return GetIpDetails("http://iframe.ip138.com/ip2city.asp", "GB2312");
    }

    public static String GetIpDetails(String publicUrl, String encodingString) {
        String ip = "";
        URLConnection conn = null;

        try {
            URL url = new URL(publicUrl);
            conn = url.openConnection();
            conn.setUseCaches(false);
            conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:********) Gecko/20110303 Firefox/3.6.15");
            conn.setRequestProperty("Content-Type", "text/html");
            conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
            InputStream is = conn.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(is, encodingString));
            String line = null;
            StringBuffer buffer = new StringBuffer();

            while ((line = reader.readLine()) != null) {
                if (line.contains("您的IP地址是")) {
                    int start = line.indexOf(91) + 1;
                    int end = line.indexOf(93);
                    ip = line.substring(start, end);
                    buffer = new StringBuffer();
                    buffer.append(ip);
                    break;
                }

                buffer.append(line);
            }

            reader.close();
            return buffer.toString();
        } catch (Exception var11) {
            var11.printStackTrace();
            return null;
        }
    }

    public static String decodeUnicode(String theString) {
        int len = theString.length();
        StringBuffer outBuffer = new StringBuffer(len);
        int x = 0;

        while (true) {
            while (true) {
                while (x < len) {
                    char aChar = theString.charAt(x++);
                    if (aChar == '\\') {
                        aChar = theString.charAt(x++);
                        if (aChar == 'u') {
                            int value = 0;

                            for (int i = 0; i < 4; ++i) {
                                aChar = theString.charAt(x++);
                                switch (aChar) {
                                    case '0':
                                    case '1':
                                    case '2':
                                    case '3':
                                    case '4':
                                    case '5':
                                    case '6':
                                    case '7':
                                    case '8':
                                    case '9':
                                        value = (value << 4) + aChar - 48;
                                        break;
                                    case ':':
                                    case ';':
                                    case '<':
                                    case '=':
                                    case '>':
                                    case '?':
                                    case '@':
                                    case 'G':
                                    case 'H':
                                    case 'I':
                                    case 'J':
                                    case 'K':
                                    case 'L':
                                    case 'M':
                                    case 'N':
                                    case 'O':
                                    case 'P':
                                    case 'Q':
                                    case 'R':
                                    case 'S':
                                    case 'T':
                                    case 'U':
                                    case 'V':
                                    case 'W':
                                    case 'X':
                                    case 'Y':
                                    case 'Z':
                                    case '[':
                                    case '\\':
                                    case ']':
                                    case '^':
                                    case '_':
                                    case '`':
                                    default:
                                        throw new IllegalArgumentException("Malformed      encoding.");
                                    case 'A':
                                    case 'B':
                                    case 'C':
                                    case 'D':
                                    case 'E':
                                    case 'F':
                                        value = (value << 4) + 10 + aChar - 65;
                                        break;
                                    case 'a':
                                    case 'b':
                                    case 'c':
                                    case 'd':
                                    case 'e':
                                    case 'f':
                                        value = (value << 4) + 10 + aChar - 97;
                                }
                            }

                            outBuffer.append((char) value);
                        } else {
                            if (aChar == 't') {
                                aChar = '\t';
                            } else if (aChar == 'r') {
                                aChar = '\r';
                            } else if (aChar == 'n') {
                                aChar = '\n';
                            } else if (aChar == 'f') {
                                aChar = '\f';
                            }

                            outBuffer.append(aChar);
                        }
                    } else {
                        outBuffer.append(aChar);
                    }
                }

                return outBuffer.toString();
            }
        }
    }
}
