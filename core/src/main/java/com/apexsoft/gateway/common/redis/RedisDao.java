package com.apexsoft.gateway.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

import java.util.concurrent.TimeUnit;

/**
 * @Author: Dinglei
 * @Description:
 * @Date: Created model 上午11:27 2018/2/6
 * @MODIFIED BY:
 */
@Repository
@Slf4j
public class RedisDao {

    @Autowired
    @Qualifier("redisTemplate")
    private RedisTemplate<Object, Object> template;

    @Autowired
    private StringRedisTemplate stringTemplate;

    public void setKey(String key, String value) {
        //先清除缓存后再存一次
        removeKey(key);
        ValueOperations<Object, Object> ops = template.opsForValue();
        ops.set(key, value);
    }

    /**
     * @param key
     * @param value
     * @param timeout 分钟
     */
    public void setKey(String key, Object value, int timeout) {
        ValueOperations<Object, Object> ops = template.opsForValue();
        ops.set(key, value, timeout, TimeUnit.MINUTES);
    }

    public Object getValue(String key) {
        log.info("从redis中获取:" + key);
        ValueOperations<Object, Object> ops = template.opsForValue();
        Object redisObj = ops.get(key);

        if (redisObj != null)
            log.info("key=【" + key + "】从redis中获取到值:" + redisObj.toString());
        else
            log.info("key=【" + key + "】从redis中获取到null");

        return redisObj;
    }

    public void removeKey(String key) {
        template.delete(key);
    }
}
