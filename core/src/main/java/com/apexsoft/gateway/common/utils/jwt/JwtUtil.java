package com.apexsoft.gateway.common.utils.jwt;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import com.apexsoft.gateway.common.redis.RedisStringDao;
import com.apexsoft.gateway.common.utils.common.Status;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import io.jsonwebtoken.security.Keys;
import java.nio.charset.StandardCharsets;


import javax.crypto.SecretKey;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.security.Key;
import java.util.Date;

/**
 * <p>
 * JWT 工具类
 * </p>
 *
 * @package: com.xkcoding.rbac.security.util
 * @description: JWT 工具类
 * @author: yangkai.shen
 * @date: Created in 2018-12-07 13:42
 * @copyright: Copyright (c) 2018
 * @version: V1.0
 * @modified: yangkai.shen
 */
@EnableConfigurationProperties(JwtConfig.class)
@Configuration
@Slf4j
public class JwtUtil {
    @Autowired
    private JwtConfig jwtConfig;
    @Autowired
    RedisStringDao redisStringDao;
    private static  String REDIS_JWT_KEY_PREFIX ;

    @Value("${app.redis_cache_prefix}")
    private String redisPrefix;

    private static  SecretKey KEY ;

    @PostConstruct
    private void init() {
        REDIS_JWT_KEY_PREFIX = redisPrefix + "security:jwt:";
        // 假设这是你的密钥配置
        String secretKeyString = "eZb1sF7tq0Hma4IY+ptGvh5jwPYv2U0i1v7+H3V6ECuJkLz0xxlBCxkJg8hyEVhS"; // 256位密钥

        // 使用密钥生成 SecretKey 对象
        KEY = Keys.hmacShaKeyFor(secretKeyString.getBytes(StandardCharsets.UTF_8));

    }


//
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;

    /**
     * 创建JWT
     *
     *
     * @param id          用户id
     * @param toUser         想要连接到的柜员id

     * @return JWT
     */
    public String createJWT( String id,String toUser) {
        Date now = new Date();

// 使用 Keys.secretKeyFor() 获取正确的密钥

        JwtBuilder builder = Jwts.builder()
                .setId(id)
                .claim("toUser", toUser)
                .setIssuedAt(now)
                .signWith(KEY);  // 使用生成的密钥对象

        // 设置过期时间
        int ttl = jwtConfig.getTtl();
        if (ttl > 0) {
            builder.setExpiration(DateUtil.offsetMinute(now, ttl));
        }

        String jwt = builder.compact();
        // 将生成的JWT保存至Redis
        redisStringDao.setKey(REDIS_JWT_KEY_PREFIX +id, jwt,ttl);

        return jwt;
    }

//    /**
//     * 创建JWT
//     *
//     * @param authentication 用户认证信息
//     * @param rememberMe     记住我
//     * @return JWT
//     */
//    public String createJWT(Authentication authentication, Boolean rememberMe) {
//        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
//        return createJWT(rememberMe, userPrincipal.getId(), userPrincipal.getUsername(), userPrincipal.getRoles(), userPrincipal.getAuthorities());
//    }

    /**
     * 解析JWT
     *
     * @param jwt JWT
     * @return {@link Claims}
     */
    public Claims parseJWT(String jwt) {
        try {
            // 使用密钥生成 SecretKey 对象

            Claims claims =  Jwts.parser().verifyWith(KEY).build().parseSignedClaims(jwt).getPayload();

            String id = claims.getId();
            String redisKey = REDIS_JWT_KEY_PREFIX + id;



            // 校验redis中的JWT是否与当前的一致，不一致则代表用户已注销/用户在不同设备登录，均代表JWT已过期
            String redisToken = redisStringDao
                    .getValue(redisKey);
            if (!StrUtil.equals(jwt, redisToken)) {
                throw new SecurityException(Status.TOKEN_OUT_OF_CTRL);
            }
            return claims;
        } catch (ExpiredJwtException e) {
            log.error("Token 已过期");
            throw new SecurityException(Status.TOKEN_EXPIRED);
        } catch (UnsupportedJwtException e) {
            log.error("不支持的 Token");
            throw new SecurityException(Status.TOKEN_PARSE_ERROR);
        } catch (MalformedJwtException e) {
            log.error("Token 无效");
            throw new SecurityException(Status.TOKEN_PARSE_ERROR);
        } catch (SignatureException e) {
            log.error("无效的 Token 签名");
            throw new SecurityException(Status.TOKEN_PARSE_ERROR);
        } catch (IllegalArgumentException e) {
            log.error("Token 参数不存在");
            throw new SecurityException(Status.TOKEN_PARSE_ERROR);
        }
    }

//    /**
//     * 设置JWT过期
//     *
//     * @param request 请求
//     */
//    public void invalidateJWT(HttpServletRequest request) {
//        String jwt = getJwtFromRequest(request);
//        String username = getUsernameFromJWT(jwt);
//        // 从redis中清除JWT
//        redisStringDao.delete(REDIS_JWT_KEY_PREFIX + username);
//    }

    /**
     * 根据 jwt 获取用户名
     *
     * @param jwt JWT
     * @return 用户名
     */
    public String getUsernameFromJWT(String jwt) {
        Claims claims = parseJWT(jwt);
        return claims.getSubject();
    }

    /**
     * 从 request 的 header 中获取 JWT
     *
     * @param request 请求
     * @return JWT
     */
    public String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StrUtil.isNotBlank(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

}
