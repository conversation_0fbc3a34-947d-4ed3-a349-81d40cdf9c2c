package com.apexsoft.gateway.common.config;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;


public class CommonConfig {

	//缓存数据库配置
	private static Map<String,String> configMap= new HashMap();

	public static String getString(String propertiesKey,String v){
		String _value="";
		if(StringUtils.isNotEmpty(configMap.get(propertiesKey))){
			_value = configMap.get(propertiesKey);
		}else{
			_value = v;
		}
	    return _value;
	}

	public static String getString(String propertiesKey){
		return getString(propertiesKey,"");
	}

	public static boolean getBoolean(String propertiesKey){
		if(StringUtils.isNotEmpty(configMap.get(propertiesKey))){
			try {
				return Boolean.parseBoolean(configMap.get(propertiesKey));
			}catch (Exception e1){
				return false;
			}
		}else {
			return false;
		}
	}

	public static int getInt(String propertiesKey){
		try{
			return getInt(propertiesKey,0);
		}catch(Exception e){
			return 0;
		}
	}

	public static int getInt(String propertiesKey,int t){
		if(StringUtils.isNotEmpty(configMap.get(propertiesKey))) {
			try {
				return Integer.parseInt(configMap.get(propertiesKey));
			}catch (Exception e1){
				return t;
			}
		}else {
			return t;
		}
	}

	public static float getFloat(String propertiesKey){
		if(StringUtils.isNotEmpty(configMap.get(propertiesKey))){
			try {
				return Float.parseFloat(configMap.get(propertiesKey));
			}catch (Exception e1){
				return 0F;
			}
		}else{
			return 0F;
		}
	}

	public static double getDouble(String propertiesKey){
		if(StringUtils.isNotEmpty(configMap.get(propertiesKey))){
			try {
				return Double.parseDouble(configMap.get(propertiesKey));
			}catch (Exception e1){
				return 0L;
			}
		}else {
			return 0L;
		}
	}

}
