package com.apexsoft.gateway.common.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStreamReader;

@Slf4j
public class UtilService {

    public static String getKhlb(String khlb, String cpbz){
        if("1".equals(khlb)){
            if("1".equals(cpbz)){
                khlb = "2";
            }else if("2".equals(cpbz)){
                khlb = "5";
            }
        }
        return khlb;
    }

    public static JSONObject getData(HttpServletRequest request) throws IOException {
        InputStreamReader is = null;
        try {
            is = new InputStreamReader(request.getInputStream(), request.getCharacterEncoding());
            char[] chars = new char[8 * 1024];
            int len;
            StringBuilder sb = new StringBuilder();
            while ((len = is.read(chars)) != -1) {
                sb = sb.append(chars, 0, len);
            }
            return JSONObject.parseObject(sb.toString());

        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }
}
