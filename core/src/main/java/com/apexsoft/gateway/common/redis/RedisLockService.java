package com.apexsoft.gateway.common.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName: RedisLockService
 * @Description: <p>
 * 江山如此多娇，引无数英雄竞折腰--XFH
 */
@Service
public class RedisLockService {

    public String LOCK_KEY = "ORDER_LOCK";

    @Autowired
    RedisStringDao redisStringDao;

    //获取锁定标识
    public String getLockValue(){
        return ""+(System.currentTimeMillis()+1000L*10L);
    }

    /**
     * 枷锁
     * @param value
     * @return
     */
    public boolean lock(String value){
        return lock(LOCK_KEY,value);
    }

    /**
     * 枷锁
     * @param value
     * @return
     */
    public boolean lock(String key,String value){
        return redisStringDao.lock(key,value);
    }
    /*
    解锁
     */
    public void unlock(String value){
        redisStringDao.unlock(LOCK_KEY,value);
    }
    /*
    解锁
     */
    public void unlock(String key,String value){
        redisStringDao.unlock(key,value);
    }
}
