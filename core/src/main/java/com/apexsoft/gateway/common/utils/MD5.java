package com.apexsoft.gateway.common.utils;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class MD5 {
	private static String encStr(byte source[]) throws IOException {
		char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
				'a', 'b', 'c', 'd', 'e', 'f' };
		MessageDigest md;
		try {
			md = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException e) {
			throw new IOException((new StringBuilder()).append("").append(e)
					.toString());
		}
		md.update(source);
		byte tmp[] = md.digest();
		char str[] = new char[32];
		int k = 0;
		for (int i = 0; i < 16; i++) {
			byte byte0 = tmp[i];
			str[k++] = hexDigits[byte0 >>> 4 & 0xf];
			str[k++] = hexDigits[byte0 & 0xf];
		}

		return (new String(str));
	}

	// 生成MD5加密
	public static String getMD5Digest(String str) throws IOException {
		return encStr(str.getBytes());
	}

	// 生成token，防止修改编号任意查看
	public static String getVideoToken(String video) throws IOException {
		SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd");
		String sMD5Str = video + ".CIF.VIDEO." + f.format(new Date());
		return getMD5Digest(sMD5Str);
	}
	
	public static void main(String args[]) {
		try {
			System.out.println(MD5.getMD5Digest("2022111114014010769"));//2013090910493001976 2021102909300105860
			System.out.println(MD5.getVideoToken("2024032809142741160"));//2013090910493001976 2021102909300105860
			System.out.println(MD5.getVideoToken("admin"));
			String userId = MD5.getVideoToken("admin");
			System.out.println(userId.equals("f199ff4a752d30815c7880cc8c98e58d"));

		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}

//local=2013071715454916602
//2020033017225801029
//2020040110350401033
//http://*************:8090/CheckVedioService?local=2013101011121621034
//http://host:8090/MediaServ?local=2013071715454916602&token=0d4a563263da8cd96ed19714a65da703