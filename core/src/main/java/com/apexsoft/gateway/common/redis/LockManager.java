package com.apexsoft.gateway.common.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 锁管理器
 * <AUTHOR>
 *
 */
@Component
public class LockManager {

	protected static final Logger log = LoggerFactory.getLogger(LockManager.class);

	@Resource
	RedisTemplate redisTemplate;

	@Resource
	RedisDao redisDao;
	/**
	 * 获取redis锁值，有值表示有锁
	 *
	 * @param lockKey        锁住的key
	 * @return
	 */
	public int lock(String lockKey) {
		Object value = redisDao.getValue(lockKey);
		if(value!=null){
			return -1;
		}else{
			redisDao.setKey(lockKey,UUID.randomUUID().toString(),60);
			return 1;
		}
	}

	public int getInitLock(String lockKey){
		int code = 0;
		int retry = 60;
		while(retry>0){
			code = lock(lockKey);
			if(code==-1){
				//-1代表：请求处理被锁,等待5次取锁
				retry--;
			}else{
				//1代表：成功取锁，-2代表：redia服务异常
				return code;
			}
			try{
				Thread.sleep(1000);
			}catch(Exception e){
				e.printStackTrace();
			}
		}
		//-9代表：取锁5次超时
		return -9;
	}

	public void delete(String lockKey){
		redisTemplate.delete(lockKey);
	}
}
