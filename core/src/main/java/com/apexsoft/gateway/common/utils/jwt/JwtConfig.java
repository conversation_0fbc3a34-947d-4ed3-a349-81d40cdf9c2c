package com.apexsoft.gateway.common.utils.jwt;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <p>
 * JWT 配置
 * </p>
 *
 * @package: com.xkcoding.rbac.security.config
 * @description: JWT 配置
 * @author: yangkai.shen
 * @date: Created in 2018-12-07 13:42
 * @copyright: Copyright (c) 2018
 * @version: V1.0
 * @modified: yangkai.shen
 */
@ConfigurationProperties(prefix = "ws.jwt")
@Data
public class JwtConfig {
    /**
     * jwt 加密 key，默认值：apexydzybzb.
     */
    private String key = ">Rk\\ZwV$Y`ynFwkrc].gF-MQW(F%/]V>(%.O;`tAI%[&Lf#$.Y^dBnwWd\\V=B*+49>^)qkmtY``0P5\\2Gs,gsr+NvsAnHD-GDOE$w@_YWhlqP2+z^^IM(GMueADQTm77xZf<_w+%]r4}jZiHl75T6?ul.%Ip\\*;bwu77k7/R[?@9q#]&[|is^$z^-Fxi?:|:eF:gqp9_1GRf96@&@s*INi$VcZPNLRg[Yi50O0JKm+bbjudNg=x*r'UV3!!ac*6s";

    /**
     * jwt 过期时间，默认值：10 {@code 30 分钟}.
     */
    private int ttl = 30;


}
