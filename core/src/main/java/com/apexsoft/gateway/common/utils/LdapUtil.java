package com.apexsoft.gateway.common.utils;

import com.apexsoft.gateway.model.JSONResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class LdapUtil {
    @Resource
    LdapTemplate ldapTemplate;

/*    public JSONResponse login(String url, String username, String password){
        JSONResponse out = new JSONResponse(JSONResponse.CODE_FAIL, "");
        LdapContext dirContext = null;
        DirContext ctx = null;
        Control[] sortConnCtls =new SortControl[1];

        try {
            Hashtable<String, String> env1 =new Hashtable<String, String>();
            env1.put(Context.PROVIDER_URL, url);
            env1.put(Context.SECURITY_PRINCIPAL, "uatapexadmin");
            env1.put(Context.SECURITY_CREDENTIALS, "Aa123456");
            env1.put(Context.SECURITY_AUTHENTICATION, "simple");
            env1.put("java.naming.batchsize","50");
            env1.put("com.sun.jndi.ldap.connect.timeout","3000");
            env1.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
            env1.put("com.sun.jndi.ldap.connect.pool","true");

            sortConnCtls[0] = new SortControl("sAMAccountName", Control.CRITICAL);
            dirContext = new InitialLdapContext(env1, sortConnCtls);
            dirContext.setRequestControls(sortConnCtls);
            SearchControls controls = new SearchControls();
            controls.setSearchScope(SearchControls.SUBTREE_SCOPE);
            String filter = "(sAMAccountName=" + username +")";
            NamingEnumeration<?> answer = dirContext.search("", filter, controls);
            String userDN = null;
            while (answer.hasMore()) {
                userDN = ((NameClassPair) answer.nextElement()).getName();
            }
            log.info("用户全名："+userDN);

            Hashtable<String,String> env = new Hashtable<String,String>();
            env.put(Context.SECURITY_AUTHENTICATION,"simple"); // LDAP访问安全级别(none,simple,strong);
            env.put(Context.SECURITY_PRINCIPAL,userDN); // AD的用户名
            env.put(Context.SECURITY_CREDENTIALS,password); // AD的密码
            env.put(Context.INITIAL_CONTEXT_FACTORY,"com.sun.jndi.ldap.LdapCtxFactory"); // LDAP工厂类
            env.put("com.sun.jndi.ldap.connect.timeout","3000");// 连接超时设置为3秒
            env.put(Context.PROVIDER_URL, url);// 默认端口389
            ctx = new InitialDirContext(env);// 初始化上下文
            out = new JSONResponse(JSONResponse.CODE_SUCCESS, "身份验证成功!");
        } catch (AuthenticationException e) {
            String errorMsg = e.getMessage();
            log.error(errorMsg,e);
            out = new JSONResponse(JSONResponse.CODE_FAIL, getLoginErrorMsg(errorMsg));
        }catch (javax.naming.CommunicationException e) {
            log.error(e.getMessage(),e);
            out = new JSONResponse(JSONResponse.CODE_FAIL, "AD域连接失败");
        }catch (Exception e) {
            log.error(e.getMessage(),e);
            out = new JSONResponse(JSONResponse.CODE_FAIL, "身份验证未知异常["+e.getMessage()+"]");
        }finally{
            if(null!=ctx){
                try {
                    ctx.close();
                    ctx=null;
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        return out;
    }*/

    public JSONResponse login(String username, String password){
        JSONResponse out = new JSONResponse(JSONResponse.CODE_FAIL, "账号密码不正确");
        try{
            EqualsFilter filter = new EqualsFilter("sAMAccountName", username);
            ldapTemplate.setIgnorePartialResultException(true);
            boolean flag = ldapTemplate.authenticate("", filter.toString(), password);
            if(flag){
                out = new JSONResponse(JSONResponse.CODE_SUCCESS, "验证通过");
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
            out = new JSONResponse(JSONResponse.CODE_FAIL, e.getMessage());
        }
        return out;
    }

    private String getLoginErrorMsg(String exceptionMsg){
        String msg = "身份认证失败";
        String[] exceptionMsgs = exceptionMsg.split("data");
        if(exceptionMsgs.length>=2){
            String[] tempStrs = exceptionMsgs[1].split(",");
            String temp = tempStrs[0].trim();
            if("525".equals(temp)){
                msg = "用户不存在";
            } else if ("52e".equals(temp)) {
                msg = "账号或密码不正确";
            } else if ("530".equals(temp)) {
                msg = "此时不允许登录";
            } else if ("531".equals(temp)) {
                msg = "在此工作站上不允许登录";
            } else if ("532".equals(temp)) {
                msg = "密码过期";
            } else if ("533".equals(temp)) {
                msg = "账户禁止使用";
            } else if ("701".equals(temp)) {
                msg = "账户已过期";
            } else if ("773".equals(temp)) {
                msg = "用户必须重置密码";
            } else if ("775".equals(temp)) {
                msg = "用户账户锁定";
            }
        }
        return msg;
    }
}
