package com.apexsoft.gateway.common.utils;


import lombok.extern.slf4j.Slf4j;

/**
 * 常量定义类。
 *
 * <AUTHOR>
 */
@Slf4j
public class OkHttpUtil {

    /*public static String get(String url) throws IOException {
        OkHttpClient okHttpClient = new OkHttpClient();
        Request request = new Request.Builder().url(url).build();
        Response response = okHttpClient.newCall(request).execute();

        if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
        Headers responseHeaders = response.headers();
        String body = response.body().string();
        log.info("httpget请求,请求url[{}],响应参数[{}]", url, body);
        return body;
    }*/
}
