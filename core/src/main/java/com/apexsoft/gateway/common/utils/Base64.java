package com.apexsoft.gateway.common.utils;

 
import java.io.UnsupportedEncodingException;

public class Base64{
    private static char[] EncodeArray_test = new char[64];
    private static byte [] DecodeArray_test=new byte[256];
    private static String EncodeStr_test="";
    
    
    public static byte[] decode(String str) throws UnsupportedEncodingException {
    	
    	byte DecodeArray[]=new byte[256];
    	String EncodeStr="";
    	char[] EncodeArray = new char[64];
    	 
        EncodeStr="STabG4X53AYZeoHI6pKuhE7qrj8FBstOPM2NyzklRCdvJLD01cwfQxU9VWmnig+/";  //加密串
        EncodeArray=EncodeStr.toCharArray();
         
    	
    	 int indx = 0;
         for (;indx< 64;indx++)
            DecodeArray[(int)EncodeArray[indx]] = (byte) indx;
            
            
        StringBuffer sb = new StringBuffer();
        byte[] data = str.getBytes("US-ASCII");
        int len = data.length;
        int i = 0;
        int b1, b2, b3, b4;
        while (i < len) {
           
            do {
                b1 = DecodeArray[data[i++]];
            } while (i < len && b1 == -1);
            if (b1 == -1) break;
           
            do {
                b2 = DecodeArray
                        [data[i++]];
            } while (i < len && b2 == -1);
            if (b2 == -1) break;
            sb.append((char) ((b1 << 2) | ((b2 & 0x30) >>> 4)));
           
            do {
                b3 = data[i++];
                if (b3 == 61) return sb.toString().getBytes("iso8859-1");
                b3 = DecodeArray[b3];
            } while (i < len && b3 == -1);
            if (b3 == -1) break;
            sb.append((char) (((b2 & 0x0f) << 4) | ((b3 & 0x3c) >>> 2)));
           
            do {
                b4 = data[i++];
                if (b4 == 61) return sb.toString().getBytes("iso8859-1");
                b4 = DecodeArray[b4];
            } while (i < len && b4 == -1);
            if (b4 == -1) break;
            sb.append((char) (((b3 & 0x03) << 6) | b4));
        }
        return sb.toString().getBytes("iso8859-1");
    }
    
    
    /*public static void main(String[] args) throws UnsupportedEncodingException {
     
    	  String s = "HjmSIak=";
    	  System.out.println("密文：" + s);
        String x1 = new String(decode(s));
        System.out.println("解密后：" + x1);
 
    	 
    }*/
}