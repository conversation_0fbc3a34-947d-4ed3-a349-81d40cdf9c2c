package com.apexsoft.gateway.common.utils;

import java.io.File;

/**
 * 常量定义类。
 *
 * <AUTHOR>
 */
public interface Constant {

    // 柜员登录redis_cache_prefix
    String REDIS_CACHE_PREFIX = "QUERY";
    int REDIS_TIMEOUT = 30;

    // 消息类型
    String NOTIFICATION_TYPE_MESSAGE = "Message";

    // 柜员头像存放路径
    String USER_PHOTO_PATH = "image" + File.separator + "user" + File.separator + "photo";

    String FQQD_4 = "4";// WEB

    String YWSL_TYPE_JGYY = "jgyy";
    String YWSL_TYPE_YGT = "ygt";

    String QUERYWORKFLOW_TYPE_TODO = "todo";
    String QUERYWORKFLOW_TYPE_INITIATED = "initiated";
    String QUERYWORKFLOW_TYPE_PARTICIPATE = "participate";
    String QUERYWORKFLOW_TYPE_COMMUNICATE = "communicate";
    String QUERYWORKFLOW_TYPE_TOVIEW = "toview";

    String LIVEBOS_HIDECANCELBTN = "&HideCancelBtn=true";
    String LIVEBOS_UIMODE_WFLIVEGRAPH = "UI.WF.LIVEGRAPH";
    String LIVEBOS_UIMODE_WFSTEP = "Ul.WF.StepMode";

    String lang_en = "en";
    String lang_TW = "zh_TW";
    String lang_CN = "zh_CN";

    String tempDic = "temp";
    String imageDic = "image/imagecache";

    int KHDJ_DEFAULT = 0;
    String YWDJ_DEFAULT = "0";

    // 外部登录用
    String LOGIN_MODE_CUSTOMER_SELF = "customerSelf";

    // cif单点登录用
    String LOGIN_MODE_CIF_TOKEN = "cifToken";

    // 视频见证人柜员属性
    String WITNESS_GYSX = "3";

    String MENU_ZXJNZ = "11";
    String MENU_JZRYGL = "12";
    String MENU_ZXPDGZ = "13";
    String MENU_JZHSPZ = "14";
    String MENU_XZHSPZ = "15"; // 揭示信息配置
    String MENU_ZXDDGL = "21";
    String MENU_ZXRWCX = "22";
    String MENU_SJZDGL = "31";
    String MENU_XTCSGL = "32";

    int ACTION_ADD = 1;
    int ACTION_REMOVE = 2;
    int ACTION_MODIFY = 3;
    int ACTION_QUERY = 4;

    // 坐席状态-空闲
    int AGENT_STATUS_FREE = 1;
    // 坐席状态-示忙
    int AGENT_STATUS_BUSY = 2;
}
