package com.apexsoft.gateway.common.schedule;

import com.apexsoft.gateway.common.utils.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


import java.io.File;
import java.io.IOException;

/**
 * 影像定时任务。
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ImageJob {



    @Scheduled(cron ="00 45 09 * * ?")
    public void deleteImageCache() {
        try {
            File _imageFile = new File(Constant.imageDic + File.separator);
            if (_imageFile.exists()) {
                FileUtils.cleanDirectory(_imageFile);
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
    }
}
