package com.apexsoft.gateway.common.utils;

import com.alibaba.fastjson.JSONObject;

import org.apache.commons.codec.binary.Base64;
import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.UUID;

/**
 * <AUTHOR>
 * @ClassName: NxRsaEncrypt
 * @Description: <p>
 * 江山如此多娇，引无数英雄竞折腰--XFH
 */
public class NxRsaEncrypt {
    private static final char[] bcdLookup = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D',
            'E', 'F' };

    private  static String nxpublicKey ="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAua+zPAvWwOhKZH2A0nXcA08jYgXRd4l8H9AxHSmA/Vsux0xtC6mOPvkpSR2h87p2KZEWwPUy+DQmbLEGN6K6e5IzbZjqq7GQ314LyWW6l99jtr4u3i2MYkfgfQRLsHVP7NZPNAvPzXSp5Zcz65YeT/myvYc9++YMBsadTd15X4E8X4oAUEQ8OoEL+Tnayy9hweR0DD2cRw47BqYzLvxDJkiZrLeeRf9ZWcBo3YlnjuWepX3rgkO/zORDHXUpKjm67KypSJlC1VFz82zO9gBxH5LPzMFo9O5XXUmGgqyi/qLqjCdX21htz48ly8fx8YIC8cSUBZic2+Hn+QMCECFeEQIDAQAB";

    public static String getNxPlugInRsaStr(String str,String publicKey){
        String outStr ="";
        try {
            //base64编码的公钥
            byte[] decoded = Base64.decodeBase64(publicKey);
            RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(decoded));
            //RSA加密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            byte[] bytes = cipher.doFinal(str.getBytes("UTF-8"));
            //outStr = Base64.encodeBase64String(bytes);
            outStr = bytesToHexStr(bytes);
        }catch (Exception e){
            e.printStackTrace();
        }
        return outStr;
    }









    public static String getToken(){
        String recToken ="";
        try {
            JSONObject str = new JSONObject();
            long t1 = System.currentTimeMillis() / 1000 + 24 * 3600;
            String sid= UUID.randomUUID().toString();
            str.put("SID", sid);
            str.put("CID", "apexsoft");
            str.put("ValidTime", t1);
            recToken = "sid="+sid+"&&cid=apexsoft&tk="+getNxPlugInRsaStr(str.toJSONString(),nxpublicKey);
        }catch (Exception e){
            e.printStackTrace();
        }
        return  recToken;
    }

    public static final String bytesToHexStr(byte[] bcd) {
        StringBuffer s = new StringBuffer(bcd.length * 2);

        for (int i = 0; i < bcd.length; i++) {
            s.append(bcdLookup[(bcd[i] >>> 4) & 0x0f]);
            s.append(bcdLookup[bcd[i] & 0x0f]);
        }

        return s.toString();
    }

   /* public static void main(String[] args) throws Exception {

        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAua+zPAvWwOhKZH2A0nXcA08jYgXRd4l8H9AxHSmA/Vsux0xtC6mOPvkpSR2h87p2KZEWwPUy+DQmbLEGN6K6e5IzbZjqq7GQ314LyWW6l99jtr4u3i2MYkfgfQRLsHVP7NZPNAvPzXSp5Zcz65YeT/myvYc9++YMBsadTd15X4E8X4oAUEQ8OoEL+Tnayy9hweR0DD2cRw47BqYzLvxDJkiZrLeeRf9ZWcBo3YlnjuWepX3rgkO/zORDHXUpKjm67KypSJlC1VFz82zO9gBxH5LPzMFo9O5XXUmGgqyi/qLqjCdX21htz48ly8fx8YIC8cSUBZic2+Hn+QMCECFeEQIDAQAB";
       JSONObject str = new JSONObject();
        str.put("SID","10");
        str.put("CID","apexsoft");
        long t1 = System.currentTimeMillis()/1000+24*3600*100;
        str.put("ValidTime",t1);
        String nxString = new NxRsaEncrypt().getNxPlugInRsaStr(str.toJSONString(),publicKey);

        System.out.println(nxString);
        System.out.println(getToken());

    }*/
}
