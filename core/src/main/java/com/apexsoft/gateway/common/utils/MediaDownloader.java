package com.apexsoft.gateway.common.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;
@Configuration
public class MediaDownloader {

    @Value("${application.tempDic}")
    private  String tempDic;
    // 定义本地保存路径
    private static String staticTempDic;
    // 定义前端 URL 前缀（例如，静态资源服务器的 URL）
    private static final String BASE_URL = "/media/";
    @PostConstruct
    public void init() {
        staticTempDic = tempDic;  // 在初始化后将值赋给静态字段
    }

    public static String downloadAndSave(String fileUrl, String defaultFileName) throws IOException {
        // 从URL中提取原始文件名（不包括完整路径）
        // 提取原始文件名和扩展名
        String originalFileName = extractOriginalFileName(fileUrl).replaceAll("\\.[^.]*$", ""); // 去掉扩展名部分
        String fileExtension = extractFileExtension(fileUrl);

        // 组合文件名：原始文件名 + "_" + 外部提供的defaultFileName
        // 生成唯一文件名
        String fileName = generateUniqueFileName(originalFileName, defaultFileName, fileExtension);

        Path filePath = Paths.get(staticTempDic, fileName);

        // 确保保存目录存在
        Files.createDirectories(filePath.getParent());

        // 检查文件是否已经存在
        File existingFile = new File(staticTempDic, fileName);
        if (existingFile.exists()) {
            // 如果文件已经存在，直接返回文件的 URL
            return BASE_URL + fileName;
        }

        // 下载文件
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        try (InputStream inputStream = connection.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(filePath.toFile())) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        // 构造前端可访问的 URL
        return BASE_URL + fileName;
    }

    /**
     * 从给定的 URL 中提取原始文件名（包括扩展名）
     * @param fileUrl 完整的文件 URL
     * @return 原始文件名（包括扩展名）
     */
    private static String extractOriginalFileName(String fileUrl) {
        // 提取路径中最后一部分作为文件名
        return fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
    }

    /**
     * 从给定的 URL 提取文件扩展名
     * @param fileUrl 完整的文件 URL
     * @return 文件扩展名（带点，例如 .mp4），如果没有扩展名则返回空字符串
     */
    private static String extractFileExtension(String fileUrl) {
        String fileName = extractOriginalFileName(fileUrl);
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex != -1) ? fileName.substring(dotIndex) : ""; // 保留扩展名前的点
    }

    /**
     * 生成唯一的文件名
     * @param originalFileName 原始文件名（不包括扩展名）
     * @param defaultFileName 外部提供的默认文件名
     * @param fileExtension 文件扩展名（例如 .mp4）
     * @return 唯一的文件名
     */
    private static String generateUniqueFileName(String originalFileName, String defaultFileName, String fileExtension) {
        // 清理 defaultFileName，移除非法字符
        String sanitizedDefaultName = defaultFileName.replaceAll("[^a-zA-Z0-9_\\-]", "_");

        // 确保 defaultFileName 不为空
        if (sanitizedDefaultName.isEmpty()) {
            sanitizedDefaultName = "download";
        }

        // 组合最终的文件名
        return String.format("%s_%s%s", originalFileName, sanitizedDefaultName, fileExtension);
    }
}
