package com.apexsoft.gateway.filter;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Slf4j
@Component
@Order(1)
public class CustomerTokenFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtil jwtUtil;

    private static final String[] PROTECTED_PATHS = {"/customer", "/ai-common"};

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String path = request.getRequestURI();

        // 检查是否需要进行token验证
        boolean needsAuth = false;
        for (String protectedPath : PROTECTED_PATHS) {
            if (path.startsWith(protectedPath)) {
                needsAuth = true;
                break;
            }
        }

        if (!needsAuth) {
            filterChain.doFilter(request, response);
            return;
        }

        // 从请求中获取token
        String token = request.getHeader("token");


        if (StringUtils.isBlank(token)) {
            handleAuthError(response, "Token不能为空");
            return;
        }

        try {
            Claims claims = jwtUtil.parseJWT(token);
            if (claims == null || StringUtils.isBlank(claims.getId())) {
                handleAuthError(response, "无效的Token");
                return;
            }
            // 将用户ID添加到请求属性中
            request.setAttribute("userId", claims.getId());
            
            // token验证通过，继续处理请求
            filterChain.doFilter(request, response);

        } catch (Exception e) {
            log.error("Token验证失败", e);
            handleAuthError(response, "Token验证失败");
        }
    }

    private void handleAuthError(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        CommonResponse errorResponse = new CommonResponse(JSONResponse.CODE_FAIL, message);
        PrintWriter out = response.getWriter();
        out.write(JSONObject.toJSONString(errorResponse));
        out.flush();
        out.close();
    }
}
