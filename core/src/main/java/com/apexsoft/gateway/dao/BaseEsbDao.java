package com.apexsoft.gateway.dao;


import com.alibaba.fastjson.JSONObject;
import com.apexsoft.client.GatewayClient;
import com.apexsoft.client.Request;
import com.apexsoft.client.Response;
import com.apexsoft.client.exception.GatewayException;
import com.apexsoft.gateway.aas.common.session.UserSession;
import com.apexsoft.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.gateway.base.constant.Constant;
import com.apexsoft.gateway.common.utils.AddressUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.apexsoft.gateway.common.utils.SystemTool;
import org.springframework.util.StringUtils;

import java.io.OutputStream;


/**
 * 无纸化esb接口。
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BaseEsbDao {
    @Autowired
    GatewayClient esbGateway;

    public JSONObject execYgtService(JSONObject in)  {

        String serviceId = in.getString("serviceId");


        in.remove("serviceId");

        JSONObject result = new JSONObject();

        if (!StringUtils.hasLength(in.getString("czzd"))) {
            String ip = SystemTool.getSessionCzzd(UserSession.getRequestHolder());
            if (ip.trim().equals("")) {
                ip = AddressUtils.getIntranetIp(UserSession.getRequestHolder());
            }
            in.put("czzd", ip);
        }
        if(in.get("fqqd")==null)
            in.put("fqqd", Constant.FQQD_5);
        return execEsbService(serviceId, in);

    }
    public JSONObject execEsbService(String func, JSONObject reqData)  {
        JSONObject jsonSendData = this.prepareCommonData(reqData);
        JSONObject result = new JSONObject();
        if(reqData.get("fqqd")==null)
            reqData.put("fqqd", Constant.BLMS_3);
        if(reqData.get("fqr")==null)
            reqData.put("fqr", jsonSendData.getString("fqr"));
        if(reqData.get("czzd")==null)
            reqData.put("czzd", jsonSendData.getString("czzd"));
        Request request = new Request(reqData);
        request.setService(func);
        Response response = null;
        try {
            response = esbGateway.service(request);
        } catch (GatewayException e) {
            log.error(e.getMessage(),e);
            result.put("code", -1);
            result.put("note", "服务异常"+e.getMessage());
            return result;
        }
        result = response.toObject(JSONObject.class);
        if(result.getIntValue("code")==-10002){
            try {
                response = esbGateway.service(request);
            } catch (GatewayException e) {
                log.error(e.getMessage(),e);
                result.put("code", -1);
                result.put("note", "服务异常"+e.getMessage());
                return result;
            }
            result = response.toObject(JSONObject.class);
        }

        return result;
    }

    /**
     * 准备一个基本的请求参数（业务类）。
     *
     * @return 请求参数
     */
    public JSONObject prepareReqData() {
        return prepareCommonData(new JSONObject());
    }
    /**
     * 准备公共参数。
     *
     * @param data 待补充公共参数的数据
     * @return 该数据
     */
    public JSONObject prepareCommonData(JSONObject data) {
        if (!StringUtils.hasLength(data.getString("czzd"))) {
            String ip = SystemTool.getSessionCzzd(UserSession.getRequestHolder());
            if (ip.trim().equals("")) {
                ip = AddressUtils.getIntranetIp(UserSession.getRequestHolder());
            }
            data.put("czzd", ip);
        }
        //获取柜员信息
        try {
            AuthUser<JSONObject> authUser = UserSession.getUserSession();
            JSONObject obj = authUser == null?null:authUser.getUser();
            if(obj!=null && obj.get("id") != null){
                data.put("fqr", obj.get("id"));
            }else{
                data.put("fqr", "0");
            }
        } catch (Exception e) {
            data.put("fqr", "0");
        }
//        if(ip.indexOf("PAD_")>=0){
        data.put("fqqd", Constant.FQQD_5);
//        }else{
//            data.put("fqqd", Constant.FQQD_4);
//        }
        data.put("blms", Constant.BLMS_3);
        return data;
    }

    public JSONObject downloadFile(Request request, OutputStream outputStream){
        JSONObject result = new JSONObject();
        result.put("code",1);
        result.put("note","文件下载成功");
        try{
//            if(!file.getParentFile().exists())
//                file.getParentFile().mkdirs();

            esbGateway.download(request, outputStream);


        }catch (Exception e){
            log.error("调用esb下载文件失败【"+request.get("filepath", String.class)+"】",e);
            result.put("code",-1);
            result.put("note","下载头像失败");
        }

        return result;
    }
}
