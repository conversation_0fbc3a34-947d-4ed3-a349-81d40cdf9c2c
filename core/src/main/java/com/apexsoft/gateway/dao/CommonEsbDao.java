package com.apexsoft.gateway.dao;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.client.Request;
import org.springframework.stereotype.Component;

import java.io.OutputStream;

@Component
public class CommonEsbDao  extends BaseEsbDao{



    public JSONObject queryXtcs(JSONObject reqData){

        return this.execEsbService("esb.ygt.cscx.cxxtcs",reqData);
    }

    public JSONObject queryYwqq(JSONObject reqData){
        return this.execEsbService("esb.ygt.ywsl.cxywqqnr",reqData);
    }

    public JSONObject cxgyxx(JSONObject reqData){
        return this.execEsbService("esb.ygt.cxgyxx",reqData);
    }

    public JSONObject queryYwqqSj(JSONObject reqData){
        return this.execEsbService("esb.ygt.ywsl.cxywqq",reqData);
    }

    public JSONObject execGawsqForPhoto(JSONObject reqData){
        return this.execEsbService("esb.ygt.khgl.gmsfyz",reqData);
    }

    public JSONObject cxCoworkInfo(JSONObject reqData){
        return this.execEsbService("esb.ygt.query.cxCoworkInfo",reqData);
    }

    public JSONObject queryGawsqFkjg(JSONObject reqData){
        return this.execEsbService("esb.ygt.khgl.cxgmsfyzsqb",reqData);
    }

    public JSONObject queryDictionary(JSONObject reqData){
        return this.execEsbService("esb.ygt.cxsjzd",reqData);
    }

    public JSONObject queryOrg(JSONObject reqData){
        return this.execEsbService("esb.ygt.cscx.cxkczyyb",reqData);
    }

    public JSONObject getYxrwmxByYwqqid(JSONObject reqData){
        return this.execEsbService("esb.ai.getYxrwmxByYwqqid",reqData);
    }

    /**
     * 根据业务请求id获取助审任务结果
     */
    public JSONObject getZsrwjgByYwqqid(JSONObject reqData){
        return this.execEsbService("esb.ai.getZsrwjgByYwqqid",reqData);
    }

    public JSONObject cxywqqyscs(JSONObject reqData){
        return this.execEsbService("esb.ygt.query.cxywqqyscs",reqData);
    }

    public JSONObject cxkhdrywfqcsjthyy(JSONObject reqData){
        return this.execEsbService("esb.ygt.khgl.cxkhdrywfqcsjthyy",reqData);
    }

    public JSONObject comparePhoto(JSONObject reqData){
        return this.execEsbService("esb.ai.comparePhoto",reqData);
    }

    public JSONObject queryKhxx(JSONObject reqData){
        return this.execEsbService("esb.ygt.cxkhxx",reqData);
    }

    public JSONObject downfile(Request request, OutputStream outputStream){
        return this.downloadFile(request, outputStream);
    }

    /**
     * 修改业务受理请求。
     *
     * @param acceptData 业务受理参数
     * @return 结果
     */
    public JSONObject modifyAccept(JSONObject acceptData) {
        JSONObject reqData = new JSONObject();
        reqData.put("ywqq_data", acceptData);
        return this.execEsbService("esb.ygt.ywsl.ywqqxg",reqData);
    }

}
