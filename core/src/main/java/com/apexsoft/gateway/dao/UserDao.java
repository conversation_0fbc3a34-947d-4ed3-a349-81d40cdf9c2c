package com.apexsoft.gateway.dao;

import com.alibaba.fastjson.JSONObject;
import com.apex.fix.JFixComm;

import com.apexsoft.gateway.common.utils.SystemTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 关于用户的ESB访问类。
 *
 * <AUTHOR> Wu
 *
 */

@Component
@Slf4j
public class UserDao extends BaseEsbDao {

    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 验证柜员登录密码。
     *
     * @param username 用户名
     * @param password 密码
     * @return 验证结果
     */
    public JSONObject verifyPassword(String username, String password) {
        JSONObject reqData = new JSONObject();
        reqData.put("uid", username);
        reqData.put("pwd", JFixComm.encrptPwd(password));
        reqData.put("serviceId", "esb.ygt.ywsl.gydrmmyz");
        return execYgtService(reqData);
    }

    /**
     * 查询用户（即柜员）信息。
     *
     * @param gysx 柜员属性
     * @return 用户信息
     */
    public JSONObject queryUserByGysx(String gysx) {
        JSONObject reqData = new JSONObject();
        reqData.put("gysx", gysx);
        reqData.put("serviceId", "esb.ygt.cxgyxx");
        reqData.put("czzd", SystemTool.getServerIp());
        return execYgtService( reqData);
    }

    /**
     * 查询用户（即柜员）信息。
     *
     * @param username 用户名
     * @return 用户信息
     */
    public JSONObject queryUser(String username) {
        JSONObject reqData = new JSONObject();
        reqData.put("userid", username);
        reqData.put("serviceId", "esb.ygt.cxgyxx");
        return execYgtService( reqData);
    }

    /**
     * 查询用户（即柜员）信息。
     *
     * @param username 用户名
     * @return 用户信息
     */
    public JSONObject queryOAUser(String username) {
        JSONObject reqData = new JSONObject();
        reqData.put("userid", username);
        reqData.put("serviceId", "esb.wwygt.cscx.oacxzxgyxx");
        return execYgtService( reqData);
    }

    /**
     * 柜员登录用户密码修改
     * @param param
     * @return
     */

    public JSONObject passwordChange(JSONObject param){
        JSONObject in = new JSONObject();
        in.put("serviceId", "esb.ygt.ywsl.gydlmmxg");
        in.putAll(param);
        return execYgtService(in);

    }

    public String queryGyqx(String gyid){
        JSONObject in=this.prepareReqData();
        in.put("gyid", gyid);
        in.put("serviceId", "esb.ygt.cscx.cxgyqxslzd"); // esb.ygt.cxgyqxslzd
        JSONObject result=this.execYgtService(in);
        if(result.getIntValue("code")>0&&result.getIntValue("count")>0){
            String gyqxlb = result.getString("gyqxlb");
            String[] gyqxlbArr = gyqxlb.split(";");

            List<String> gyqxlbResult = new ArrayList<String>();
            for (String item: gyqxlbArr) {
                if(active.equals("dev") || active.equals("devtest")){ //不为dev环境
                    if(item.startsWith("P")){
                        gyqxlbResult.add(item);
                    }
                }else{
                    gyqxlbResult.add(item);
                }
            }
            return StringUtils.join(gyqxlbResult,";");
        }else
            return "";
    }

    /**
     * @param @param  uid
     * @param @param  flName
     * @param @param  px
     * @param @return 设定文件
     * @return Property    返回类型
     * @throws
     * @Title: addFl
     * @Description: TODO(插入分组)
     */

    public JSONObject addFl(String uid, String flmc) {
        JSONObject jsonSendData = this.prepareReqData();

        jsonSendData.put("uid",uid);
        jsonSendData.put("cdflmc",flmc);
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdflxz");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }


    /**
     * @param @param  uid
     * @param @param  flmc
     * @param @return
     * @param @throws SQLException    设定文件
     * @return Property    返回类型
     * @throws
     * @Title: delFl
     * @Description: TODO(删除)
     */

    public JSONObject delFl(String uid, String cdid) {

        JSONObject jsonSendData =this.prepareReqData();

        jsonSendData.put("uid",            uid);
        jsonSendData.put("cdflid",         cdid);
        jsonSendData.put("serviceId", "esb.ygt.zdycd.cdflsc");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }


    public JSONObject getFl(String uid) {

        JSONObject jsonSendData =this.prepareReqData();

        jsonSendData.put("uid",            uid);
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdflcx");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }

    /**
     * @param @param  uid
     * @param @param  flmc
     * @param @param  cdid
     * @param @return 设定文件
     * @return Property    返回类型
     * @throws
     * @Title: updateFlmc
     * @Description: TODO(修改名称)
     */
    public JSONObject updateFlmc(String uid, String cdid, String flmc) {

        JSONObject jsonSendData = this.prepareReqData();

        jsonSendData.put("uid",            uid);
        jsonSendData.put("cdflid",            cdid);
        jsonSendData.put("cdflmc",            flmc);
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdflxg");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }


    public JSONObject updateFlpx(String uid, String id, String px) {

        JSONObject jsonSendData = this.prepareReqData();

        jsonSendData.put("uid",            uid);
        jsonSendData.put("cdflid",            id);
        jsonSendData.put("px",            px);
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdflpxxg");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }

    /**
     * @param @param  uid
     * @param @param  cdurl
     * @param @return 设定文件
     * @return Property    返回类型
     * @throws
     * @Title: insertZDYCD
     * @Description: TODO(添加自定义菜单)
     */

    public JSONObject addCd(String uid, String cdflid, String cdurl) {

        JSONObject jsonSendData = this.prepareReqData();

        jsonSendData.put("uid",            uid);
        jsonSendData.put("cdurl",            cdurl);
        jsonSendData.put("cdflid",            cdflid);
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdxz");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }


    /**
     *

     * @Title: delFlCDById

     * @Description: TODO(删除分组菜单)

     * @param @param uid
     * @param @param cdid
     * @param @return
     * @param @throws SQLException    设定文件

     * @return Property    返回类型

     * @throws
     */

    public JSONObject delCd(String uid, String cdid) {

        JSONObject jsonSendData = this.prepareReqData();

        jsonSendData.put("uid",            uid);
        jsonSendData.put("cdid",            cdid);
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdsc");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService( jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }



    public JSONObject getCd(String uid) {


        JSONObject jsonSendData = this.prepareReqData();

        jsonSendData.put("uid",uid);
        jsonSendData.put("cdqxyz",true); // 全部加载出来不验证菜单权限
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdcx");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }


    public JSONObject updateCdpx(String uid, String cdflid, String cdid, String px) {

        JSONObject jsonSendData = this.prepareReqData();

        jsonSendData.put("uid",            uid);
        jsonSendData.put("cdid",            cdid);
        jsonSendData.put("px",            px);
        jsonSendData.put("cdflid",            cdflid);
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdpxxg");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }


    public JSONObject modifyCdfl(String uid, String cdflid, String cdid, String px) {

        JSONObject jsonSendData = this.prepareReqData();

        jsonSendData.put("uid",            uid);
        jsonSendData.put("cdid",            cdid);
        jsonSendData.put("px",              px);
        jsonSendData.put("cdflid",            cdflid);
        jsonSendData.put("serviceId","esb.ygt.zdycd.cdflbg");
        JSONObject jsonRet;
        try {
            jsonRet = this.execYgtService(jsonSendData);
            jsonRet.put("message", jsonRet.getString("note"));
            jsonRet.remove("note");
        } catch (Exception e) {
            jsonRet = new JSONObject();
            jsonRet.put("code", -9);
            jsonRet.put("message", e.getMessage());
        }
        return jsonRet;
    }

    /**
     * 查询用户自定义菜单
     * @param userId
     * @return
     */
    public JSONObject queryUserMenuTree(String userId,String ywlx){
        JSONObject in=this.prepareReqData();
        in.put("uid", userId);
        in.put("ywlx", ywlx);
        in.put("serviceId", "esb.ygt.zdycd.cdqxscx");
        return this.execYgtService(in);
    }


    /**
     * 漫游柜员权限修改
     * @param param
     * @return
     */

    public JSONObject myqxxg(JSONObject param){
        JSONObject in = new JSONObject();
        in.put("serviceId", "esb.ygt.ywsl.gymyyybxg");
        in.putAll(param);
        return execYgtService(in);

    }
}
