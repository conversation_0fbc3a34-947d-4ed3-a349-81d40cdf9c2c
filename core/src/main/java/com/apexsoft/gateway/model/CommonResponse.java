package com.apexsoft.gateway.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by xfh on 2018/5/23.
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "响应报文")
@Data
public class CommonResponse extends JSONResponse {

//    private JSONObject data = new JSONObject();
    private Object data;
    private JSONArray records = new JSONArray();
    private int count;
    private int totalcount;

    public CommonResponse() {
        super();
    }


    public void setData(JSONObject data) {
        this.data = data;
    }

    public void setData(JSONArray data) {
        this.data = data;
    }


    public void setRecords(JSONArray records) {
        this.records = records;
        this.count = records.size();
    }

    public CommonResponse(int code, String note) {
        super.setCode(code);
        super.setNote(note);
    }
}
