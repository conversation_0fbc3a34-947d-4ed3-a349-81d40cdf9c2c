package com.apexsoft.gateway.model;

import com.apexsoft.gateway.service.dispatch.rule.om.DispatchRuleValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "派单规则值修改请求对象", description = "派单规则值修改请求对象")
@Data
public class DispatchRuleValueRequest implements Serializable {

    @ApiModelProperty(value = "数组", name = "values", required = true, example = "[]")
    private List<DispatchRuleValue> values;
}
