package com.apexsoft.gateway.model;

import com.apexsoft.gateway.service.qualitycheck.om.WitnessQualityCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "质检参数请求对象", description = "质检参数请求对象")
@Data
public class QualityCheckRequest {

    @ApiModelProperty(value = "数组", name = "values", required = true, example = "[]")
    private List<WitnessQualityCheck> values;
}
