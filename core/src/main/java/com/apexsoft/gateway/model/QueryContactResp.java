package com.apexsoft.gateway.model;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.apexsoft.LiveProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class QueryContactResp implements Serializable {
    // 账户号码
    @LiveProperty(note = "账户号码", index = 1)
    private String contactCode;

    /**
     * 主題
     */
    @LiveProperty(note = "主題", index = 2)
    private String subject;

    /**
     * 內容
     */
    @LiveProperty(note = "內容", index = 3)
    private String content;

    /**
     * 附件列表
     */
    @LiveProperty(note = "附件列表", index = 4)
    private String attachmentList;

    /**
     * 联系人
     */
    @LiveProperty(note = "联系人", index = 9)
    private String contactName;

    /**
     * 通知书发送日期
     */
    @LiveProperty(note = "通知书发送日期", index = 10)
    private Integer estimatedSendDate;

    /**
     * 账户中文名
     */
    @LiveProperty(note = "账户中文名", index = 11)
    private String nameCn;

    /**
     * 账户英文名
     */
    @LiveProperty(note = "账户英文名", index = 12)
    private String nameEn;

    /**
     * 联系人地址
     */
    @LiveProperty(note = "联系人地址", index = 13)
    private String contactAddress;

    @LiveProperty(note = "发送实例id", index = 14)
    private String serialNo;
}
