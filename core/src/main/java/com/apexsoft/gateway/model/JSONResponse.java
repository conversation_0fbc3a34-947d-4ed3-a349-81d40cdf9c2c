package com.apexsoft.gateway.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by Administrator on 2018/2/23.
 */
@ApiModel(description = "公用JSON响应报文")
public class JSONResponse {
    public static final int CODE_SUCCESS = 1;
    public static final int CODE_FAIL = -1;
    public static final int CODE_VALID = -2;
    /**
     * 统一认证失败CODE
     */
    public static final int CODE_AC_AUTH_FAIL = -9;


    @ApiModelProperty(position = 0, notes = "状态码:>0,调用成功,<0,调用失败")
    private int code = CODE_SUCCESS;
    @ApiModelProperty(position = 1, notes = "调用说明:code<0时，是失败原因")
    private String note = "";

    public JSONResponse() {
        this.code = CODE_SUCCESS;
        this.note = "";
    }

    public JSONResponse(int code, String note) {
        this.code = code;
        this.note = note;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
}

