package com.apexsoft.gateway.service.dispatch.rule.request;

import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import com.apexsoft.gateway.service.dispatch.rule.RequestDispatchRuleWithYesOrNoDict;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 见证退回优先派单
 */
@Component("1001")
@Slf4j
public class WitnessRejectPriorityDispatchRule extends RequestDispatchRuleWithYesOrNoDict {

    @Override
    protected int execPushCompare(QueueWitnessRequest o1, QueueWitnessRequest o2, List<QueueWitnessRequest> queueWitnessRequests) {
        // @todo 实现见证退回优先派单规则 还没实现
        return 0;
    }
}
