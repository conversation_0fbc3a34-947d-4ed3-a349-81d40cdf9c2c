package com.apexsoft.gateway.service.dispatch.client.om;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * ////////////////////////////////////////////////////////////////////
 * //                            _ooOoo_                             //
 * //                           o8888888o                            //
 * //                           88" . "88                            //
 * //                           (| -_- |)                            //
 * //                           O\  =  /O                            //
 * //                        ____/`---'\____                         //
 * //                      .'  \\|     |//  `.                       //
 * //                     /  \\|||  :  |||//  \                      //
 * //                    /  _||||| -:- |||||-  \                     //
 * //                    |   | \\\  -  /// |   |                     //
 * //                    | \_|  ''\---/''  |   |                     //
 * //                    \  .-\__  `-`  ___/-. /                     //
 * //                  ___`. .'  /--.--\  `. . ___                   //
 * //                ."" '<  `.___\_<|>_/___.'  >'"".                //
 * //              | | :  `- \`.;`\ _ /`;.`/ - ` : | |               //
 * //              \  \ `-.   \_ __\ /__ _/   .-` /  /               //
 * //        ========`-.____`-.___\_____/___.-`____.-'========       //
 * //                             `=---='                            //
 * //        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^      //
 * //         佛祖保佑       永无BUG        永不修改                   //
 * ////////////////////////////////////////////////////////////////////
 * Company: apexsoft
 *
 * 见证请求实体类
 *
 * <AUTHOR>
 * @date 2024/7/30 11:28
 */
@Data
@TableName("witness_request")
public class WitnessRequest implements Serializable {
    @TableId
    private Long id;
    private String clientId;
    private String joinTime;
    private Integer priority;
    // 请求状态：0，等待中；-1，客户主动取消请求；-2，无可服务坐席在线；1，撮合成功
    private Integer state;
    private String serveTime;
    private String serveAgentId;
    private String serveEndTime;
    private String result;
    private String reason;

    // 客户信息
    private String clientName;
    private int org;
    private int bizType;
    private String bizCode;
    private int cusLevel;
    private String reqData;

    private Long bizRequestId;
    private String techCode;
    private String currentQueueInfo;

    private String markerData;
}
