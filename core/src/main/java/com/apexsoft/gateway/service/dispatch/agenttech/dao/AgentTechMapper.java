package com.apexsoft.gateway.service.dispatch.agenttech.dao;

import com.apexsoft.gateway.service.dispatch.agenttech.om.AgentTech;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AgentTechMapper extends BaseMapper<AgentTech> {
    @Select("SELECT ID, tech_code, tech_name, tech_type, BIZ_LEVEL, CUS_LEVEL FROM agent_tech")
    List<AgentTech> getAll ();
}
