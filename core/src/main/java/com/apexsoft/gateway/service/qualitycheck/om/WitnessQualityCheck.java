package com.apexsoft.gateway.service.qualitycheck.om;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("witness_quality_check")
public class WitnessQualityCheck {
    // 参数代码
    private String code;
    // 参数名称
    private String name;
    // 参数值
    private String value;
    // 总分
    private Integer totalScore;
    // 单次扣分
    private Integer onceDeductedScore;
    // 两次扣分
    private Integer twiceDeductedScore;
    // 通过分
    private Integer passScore;
}
