package com.apexsoft.gateway.service.dispatch.client.dao;

import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequestView;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface WitnessRequestMapper extends BaseMapper<WitnessRequest> {

        @Insert("insert into witness_request " +
                        "(client_id, join_time, org, biz_type, biz_code, cus_level, biz_request_id," +
                        "client_name, req_data) " +
                        "values " +
                        "(#{clientId}, #{joinTime}, #{org, jdbcType=INTEGER}, #{bizType, jdbcType=INTEGER}, " +
                        "#{bizCode, jdbcType=INTEGER}, #{cusLevel, jdbcType=INTEGER}, #{bizRequestId, jdbcType=INTEGER},"
                        +
                        "#{clientName}, #{reqData})")
        int newWitness(String clientId, String joinTime, int org, int bizType, String bizCode, int cusLevel,
                        int bizRequestId, String clientName, String reqData) throws Exception;

        @Select("select * from witness_request where id = #{requestId, jdbcType=INTEGER}")
        WitnessRequest selectById(long requestId);

        @Select("select count(*) from witness_request where result != 'null' and serve_agent_id = #{serveAgentId, jdbcType=INTEGER}")
        int selectCount(String serveAgentId);

        @Select("select * from witness_request where client_id = #{clientId} order by id desc limit 1")
        WitnessRequest queryFirstClient(String clientId);

        @Update("update witness_request set state = 1, serve_agent_id = #{serveAgentId}, serve_time = #{serveTime} where id = #{requestId, jdbcType=INTEGER}")
        int accept(long requestId, String serveAgentId, String serveTime);

        // 记录见证失败的状态，-1，客户主动取消请求；-2，无可服务坐席在线；-3，其他原因导致见证请求终止；
        @Update("update witness_request set state = #{state, jdbcType=INTEGER} where id = #{requestId, jdbcType=INTEGER}")
        int witnessFailed(long requestId, Integer state);

        @Update("update witness_request set marker_data = #{markerData, jdbcType=VARCHAR} where id = #{id, jdbcType=INTEGER}")
        int modifyWitness(WitnessRequest witnessRequest);

        // 记录见证成功的结果，见证通过与否和详细原因
        @Update("update witness_request set serve_end_time = #{serveEndTime}, " +
                        "result = #{result}, reason = #{reason} where id = #{requestId, jdbcType=INTEGER}")
        int witnessResult(long requestId, String serveEndTime, String result, String reason);

        // 坐席掉线，需要更新服务中的请求的见证结果
        @Update("update witness_request set state = -3, serve_end_time = #{serveEndTime}, result = #{result}, reason = #{reason} "
                        +
                        "where serve_agent_id = #{agentId} and state = 1 and (result is null or result = '')")
        int agentOffline(String agentId, String serveEndTime, String result, String reason);

        // 查询
        // @Select({
        // "<script>",
        // " select r.*, a.agent_name, a.org from witness_request r left join
        // witness.agent a on r.serve_agent_id = a.agent_id",
        // " where state = 1",
        // " and result IS NOT NULL and result != '' ",
        // " <if test=\"clientId != null and clientId != ''\">",
        // " and client_id like '%#{clientId}%' ",
        // "</if>",
        // " <if test=\"clientName != null and clientName != ''\">",
        // " and client_name like '%#{clientName}%' ",
        // "</if>",
        // " <if test=\"serveAgentId != null and serveAgentId != ''\">",
        // " and serve_agent_id like '%#{serveAgentId}%' ",
        // "</if>",
        // " <if test=\"org != null and org != ''\">",
        // " and org in '(#{org})' ",
        // "</if>",
        // " <if test=\"serveAgentIdIn != null and serveAgentIdIn != ''\">",
        // " and serve_agent_id in '(#{serveAgentIdIn})' ",
        // "</if>",
        // " <if test=\"serveTimeBegin != null and serveTimeBegin != ''\">",
        // " and serve_time >= #{serveTimeBegin} ",
        // "</if>",
        // " <if test=\"serveTimeEnd != null and serveTimeEnd != ''\">",
        // " and serve_time <= #{serveTimeEnd} ",
        // "</if>",
        // " order by r.id desc ",
        // "</script>"
        // })
        // Page<WitnessRequestView> queryFinishedWitness (Page<WitnessRequestView> page,
        // String clientId,
        // String clientName,
        // String org,
        // String serveAgentId,
        // String serveAgentIdIn,
        // String serveTimeBegin,
        // String serveTimeEnd);

        /**
         * 查询坐席当天服务的客户列表
         * 
         * @param serveAgentId 坐席ID
         * @return 当天服务的客户列表
         */
        @Select("SELECT id, client_name, serve_time, req_data FROM witness_request " +
                        "WHERE serve_agent_id = #{serveAgentId} " +
                        "AND serve_time LIKE CONCAT(DATE_FORMAT(CURDATE(), '%Y%m%d'), '%') " +
                        "ORDER BY serve_time DESC")
        List<WitnessRequest> queryTodayServiceList(String serveAgentId);
}
