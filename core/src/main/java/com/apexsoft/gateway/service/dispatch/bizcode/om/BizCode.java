package com.apexsoft.gateway.service.dispatch.bizcode.om;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * ////////////////////////////////////////////////////////////////////
 * //                            _ooOoo_                             //
 * //                           o8888888o                            //
 * //                           88" . "88                            //
 * //                           (| -_- |)                            //
 * //                           O\  =  /O                            //
 * //                        ____/`---'\____                         //
 * //                      .'  \\|     |//  `.                       //
 * //                     /  \\|||  :  |||//  \                      //
 * //                    /  _||||| -:- |||||-  \                     //
 * //                    |   | \\\  -  /// |   |                     //
 * //                    | \_|  ''\---/''  |   |                     //
 * //                    \  .-\__  `-`  ___/-. /                     //
 * //                  ___`. .'  /--.--\  `. . ___                   //
 * //                ."" '<  `.___\_<|>_/___.'  >'"".                //
 * //              | | :  `- \`.;`\ _ /`;.`/ - ` : | |               //
 * //              \  \ `-.   \_ __\ /__ _/   .-` /  /               //
 * //        ========`-.____`-.___\_____/___.-`____.-'========       //
 * //                             `=---='                            //
 * //        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^      //
 * //         佛祖保佑       永无BUG        永不修改                   //
 * ////////////////////////////////////////////////////////////////////
 * Company: apexsoft
 *
 * <AUTHOR>
 * @date 2023/8/14 16:50
 */
@ApiModel(value = "业务代码对象", description = "具体的业务代码参数")
public class BizCode {
    @ApiModelProperty(value = "业务代码", name = "bizCode", required = true, example = "00002")
    private String bizCode;
    @ApiModelProperty(value = "业务名称", name = "bizName", example = "双人见证开户")
    private String bizName;
    @ApiModelProperty(value = "业务等级", name = "bizLevel", example = "1")
    private String bizLevel;
//    @ApiModelProperty(value = "业务等级（字符串）", name = "bizLevelStr", example = "1")
//    private String bizLevelStr;

    public BizCode(String bizCode, String bizName, String bizLevel) {
        this.bizCode = bizCode;
        this.bizName = bizName;
        this.bizLevel = bizLevel;
//        this.bizLevelStr = String.valueOf(bizLevel);
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getBizLevel() {
        return bizLevel == null ? "" : bizLevel;
    }

    public void setBizLevel(String bizLevel) {
        this.bizLevel = bizLevel;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof BizCode) {
            BizCode bizCode = (BizCode) obj;
            if (bizCode.getBizCode() != null && bizCode.getBizCode().equals(this.bizCode)) {
                return true;
            }
        }
        return super.equals(obj);
    }

    //    public String getBizLevelStr() {
//        return bizLevelStr;
//    }
//
//    public void setBizLevelStr(String bizLevelStr) {
//        this.bizLevelStr = bizLevelStr;
//    }
}
