package com.apexsoft.gateway.service.socket;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.apexsoft.gateway.service.CommonService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tencent.core.ws.Credential;
import com.tencent.core.ws.SpeechClient;
import com.tencent.ttsv2.*;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;

import com.tencentcloudapi.iai.v20200303.IaiClient;
import com.tencentcloudapi.iai.v20200303.models.*;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.nio.BufferOverflowException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import javax.imageio.ImageIO;

public class WebSocketHandler extends TextWebSocketHandler {
    @Autowired
    private JwtUtil jwtUtil;
    @Value("${tc-ai.usersign-expire}")
    private long expire;
    @Value("${tc-ai.SecretId}")
    private String secretId;
    @Value("${tc-ai.SecretKey}")
    private String SecretKey;
    @Value("${tc-ai.appid}")
    private String appId;

    @Autowired
    private CommonService commonService;

    private static final Logger logger = LoggerFactory.getLogger(WebSocketHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    // 为每个用户存储参考人脸图片
    private final Map<String, String> userReferenceFaces = new ConcurrentHashMap<>();
    private final Map<WebSocketSession, MessageSender> sessionSenders = new ConcurrentHashMap<>();

    private static final SpeechClient proxy;

    static {
        try {
            proxy = new SpeechClient(TtsConstant.DEFAULT_TTS_REQ_URL);
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        } catch (Exception e) {
            logger.error("Failed to initialize SpeechClient: {}", e.getMessage());
            throw new RuntimeException("SpeechClient initialization failed", e);
        }
    }

    /**
     * 压缩Base64编码的图片分辨率
     *
     * @param base64Image 原始Base64图片字符串
     * @param maxWidth    最大宽度
     * @param maxHeight   最大高度
     * @return 压缩后的Base64图片字符串
     */
    private String resizeBase64Image(String base64Image, int maxWidth, int maxHeight) {
        try {
            // 解码Base64字符串
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);
            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage originalImage = ImageIO.read(bis);

            // 获取原始尺寸
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();

            // 如果图片尺寸已经小于目标尺寸，则不需要压缩
            if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
                return base64Image;
            }

            // 计算新尺寸，保持宽高比
            int newWidth = originalWidth;
            int newHeight = originalHeight;

            // 如果宽度超过最大宽度，按比例缩小高度
            if (originalWidth > maxWidth) {
                newWidth = maxWidth;
                newHeight = (int) (originalHeight * ((double) maxWidth / originalWidth));
            }

            // 如果高度仍然超过最大高度，再次调整宽度和高度
            if (newHeight > maxHeight) {
                newHeight = maxHeight;
                newWidth = (int) (newWidth * ((double) maxHeight / newHeight));
            }

            // 创建新的缩放图像
            BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = resizedImage.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
            g.dispose();

            // 将缩放后的图像编码为Base64
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ImageIO.write(resizedImage, "jpg", bos);
            byte[] resizedImageBytes = bos.toByteArray();

            return Base64.getEncoder().encodeToString(resizedImageBytes);
        } catch (IOException e) {
            logger.error("压缩图片失败: {}", e.getMessage());
            return base64Image; // 如果压缩失败，返回原始图片
        }
    }

    private void handleSetReferenceFace(WebSocketSession session, MessageWrapper wrapper) throws IOException {
        try {
            String userId = (String) session.getAttributes().get("userId");
            String base64Image = (String) wrapper.getData();

            if (base64Image == null || base64Image.isEmpty()) {
                throw new IllegalArgumentException("参考人脸图片数据不能为空");
            }

            MessageWrapper response;
            JSONObject result = commonService.downloadFileToBase64(base64Image);
            if (result.getIntValue("code") < 0) {
                response = new MessageWrapper("REFERENCE_FACE_SET",
                        Collections.singletonMap("message", "参考人脸设置失败：" + result.getString("note")));
            } else {
                // 压缩图片到适合人脸识别的分辨率
                String compressedImage = resizeBase64Image(result.getString("imgBase64"), 1920, 1080);
                // 为当前用户保存参考人脸图片
                userReferenceFaces.put(userId, compressedImage);
                response = new MessageWrapper("REFERENCE_FACE_SET",
                        Collections.singletonMap("message", "参考人脸设置成功"));
            }

            // 返回成功消息
//            MessageWrapper response = new MessageWrapper("REFERENCE_FACE_SET",
//                    Collections.singletonMap("message", "参考人脸设置成功"));
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));

            logger.info("用户 {} 的参考人脸图片已更新", userId);
        } catch (Exception e) {
            logger.error("设置参考人脸失败: {}", e.getMessage());
            MessageWrapper response = new MessageWrapper("REFERENCE_FACE_SET",
                    Collections.singletonMap("error", "设置参考人脸失败: " + e.getMessage()));
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));
        }
    }

    // 存储用户会话
    private final Map<String, Set<WebSocketSession>> userSessions = new ConcurrentHashMap<>();
    private final Map<String, MessageSender> ttsMessageSenders = new ConcurrentHashMap<>();

    public MessageSender getMessageSender(String ttsId) {
        return ttsMessageSenders.get(ttsId);
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String token = extractToken(session);
        String userId = validateToken(token);

        if (userId != null) {
            userSessions.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet()).add(session);
            session.getAttributes().put("userId", userId);
            logger.info("User {} connected with session {}", userId, session.getId());
        } else {
            session.close(CloseStatus.POLICY_VIOLATION);
        }
    }

    // 添加一个Map来存储正在进行的合成任务
    private final Map<String, SpeechSynthesizer> activeSynthesizers = new ConcurrentHashMap<>();

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        MessageWrapper wrapper = objectMapper.readValue(payload, MessageWrapper.class);

        switch (wrapper.getType()) {
            case "MESSAGE":
                handleChatMessage(session, wrapper);
                break;
            case "TTS":
                handleTTSRequest(session, wrapper);
                break;

            case "SET_REFERENCE_FACE":
                handleSetReferenceFace(session, wrapper);
                break;
            case "BINARY_HEADER":
                // 存储二进制消息的头部信息
                session.getAttributes().put("messageType", wrapper.getData());
                session.getAttributes().put("contentType", wrapper.getContentType());
                String purpose = wrapper.getPurpose();
                if (purpose != null) {
                    session.getAttributes().put("purpose", purpose);
                }
                logger.info("Received binary header - messageType: {}, contentType: {}", wrapper.getData(), wrapper.getContentType());
                break;
            default:
                logger.warn("Unknown message type: {}", wrapper.getType());
        }
    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
        byte[] payload = message.getPayload().array();

        // 从会话属性中获取头部信息
        String messageType = (String) session.getAttributes().get("messageType");
        String contentType = (String) session.getAttributes().get("contentType");
        String purpose = (String) session.getAttributes().get("purpose");

        if (messageType == null || contentType == null) {
            logger.warn("Missing messageType or contentType in session attributes");
            return;
        }
        // 根据消息类型处理二进制数据
        switch (messageType) {
            case "FACEDETECTION":
                try {
                    handleFaceDetectionBinary(session, payload, purpose);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                break;

            default:
                logger.warn("Unknown binary message type: {}", messageType);
        }
    }

    private void handleFaceDetectionBinary(WebSocketSession session, byte[] imageBytes, String purpose) throws IOException {
        String userId = (String) session.getAttributes().get("userId");
        MessageSender messageSender = sessionSenders.computeIfAbsent(session, MessageSender::new);

        try {
            // 将二进制数据转换成 Base64
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            // 创建人脸检测请求
            com.tencentcloudapi.common.Credential cred = new com.tencentcloudapi.common.Credential(secretId, SecretKey);
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("iai.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            IaiClient client = new IaiClient(cred, "ap-beijing", clientProfile);

            // 设置人脸检测请求参数
            DetectFaceRequest detectFaceReq = new DetectFaceRequest();
            detectFaceReq.setMaxFaceNum(3L); // 最大人脸数量z
            detectFaceReq.setMinFaceSize(34L); // 最小人脸尺寸
            detectFaceReq.setImage(base64Image); // 设置图片
            detectFaceReq.setNeedQualityDetection(1L); // 需要质量检测
            detectFaceReq.setFaceModelVersion("3.0"); // 人脸模型版本
//
            // 调用人脸检测 API
            DetectFaceResponse detectFaceResp = client.DetectFace(detectFaceReq);

            // 检查检测结果
            if (detectFaceResp.getFaceInfos() == null || detectFaceResp.getFaceInfos().length == 0) {
                // 未检测到人脸
                MessageWrapper response = new MessageWrapper("FACEDETECTION_RESULT",
                        Collections.singletonMap("error", "未检测到人脸"));
                messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));
                return;
            }

            // 返回检测结果
            MessageWrapper detectFaceResponse = new MessageWrapper("FACEDETECTION_RESULT", detectFaceResp.getFaceInfos());
            messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(detectFaceResponse)));

            // 检查所有人脸的质量
            boolean allFacesQualified = true;
            for (int i = 0; i < detectFaceResp.getFaceInfos().length; i++) {
                FaceInfo faceInfo = detectFaceResp.getFaceInfos()[i];
                if (faceInfo.getFaceQualityInfo().getScore() <= 80) {
                    logger.info("人脸 {} 质量不合格，分数: {}", i, faceInfo.getFaceQualityInfo().getScore());
                    allFacesQualified = false;
                    break;
                }
            }

            // 只有当所有人脸质量都合格时才进行人脸比对
            if (allFacesQualified) {
                // 获取当前用户的参考人脸
                String referenceFace = userReferenceFaces.get(userId);

                // 检查是否已设置参考人脸
                if (referenceFace == null || referenceFace.isEmpty()) {
                    MessageWrapper errResp = new MessageWrapper("FACECOMPARE_RESULT",
                            Collections.singletonMap("error", "未设置参考人脸，请先设置参考人脸"));
                    messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(errResp)));
                    return;
                }

                // 创建人脸对比请求
                CompareFaceRequest compareFaceReq = new CompareFaceRequest();
                compareFaceReq.setImageA(base64Image); // 设置待对比图片
                compareFaceReq.setImageB(referenceFace); // 设置参考图片
                compareFaceReq.setFaceModelVersion("3.0"); // 人脸模型版本

                // 调用人脸对比 API
                CompareFaceResponse compareFaceResp = client.CompareFace(compareFaceReq);

                // 返回对比结果
                MessageWrapper compareFaceResponse;
                if (purpose != null) {
                    compareFaceResponse = new MessageWrapper("FACECOMPARE_RESULT",
                            new HashMap<String, Object>() {{
                                put("score", compareFaceResp.getScore());
                                put("purpose", purpose);
                            }});
                } else {
                    compareFaceResponse = new MessageWrapper("FACECOMPARE_RESULT", compareFaceResp.getScore());
                }
                messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(compareFaceResponse)));
            } else {
                // 人脸质量不合格
                MessageWrapper errResp = new MessageWrapper("FACECOMPARE_RESULT",
                        Collections.singletonMap("error", "人脸质量不合格，请重新拍摄"));
                messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(errResp)));
            }
        } catch (TencentCloudSDKException e) {
            // 处理腾讯云 SDK 异常
            logger.error("用户 {} 的人脸检测/对比失败: {}", userId, e.getMessage());
            MessageWrapper response = new MessageWrapper("FACEDETECTION_RESULT",
                    Collections.singletonMap("error", "人脸检测/对比失败: " + e.getMessage()));
            messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));
        } catch (Exception e) {
            // 处理其他异常
            logger.error("用户 {} 的人脸检测/对比发生未知错误: {}", userId, e.getMessage());
            MessageWrapper response = new MessageWrapper("FACEDETECTION_RESULT",
                    Collections.singletonMap("error", "发生未知错误: " + e.getMessage()));
            messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));
        }
    }


    private void handleChatMessage(WebSocketSession session, MessageWrapper wrapper) throws IOException {
        try {
            String data = (String) wrapper.getData();
            MessageRequest messageRequest = objectMapper.readValue(data, MessageRequest.class);
            Set<WebSocketSession> recipientSessions = userSessions.getOrDefault(messageRequest.getRecipient(), Collections.emptySet());

            if (!recipientSessions.isEmpty()) {
                MessageWrapper response = new MessageWrapper("MESSAGE", messageRequest.getContent());

                for (WebSocketSession recipientSession : recipientSessions) {
                    if (recipientSession.isOpen()) {
                        MessageSender messageSender = sessionSenders.computeIfAbsent(recipientSession, MessageSender::new);
                        messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));
                    }
                }
            }
        } catch (IllegalArgumentException e) {
            logger.error("Failed to parse MessageRequest: {}", e.getMessage());
        }
    }

    /**
     * 获取并移除指定ID的语音合成器
     *
     * @param ttsId TTS任务ID
     * @return 语音合成器实例，如果不存在则返回null
     */
    public SpeechSynthesizer getAndRemoveSynthesizer(String ttsId) {
        return activeSynthesizers.remove(ttsId);
    }

    private void handleTTSRequest(WebSocketSession session, MessageWrapper wrapper) throws IOException {
        String data = (String) wrapper.getData();
        TTSRequest ttsRequest = objectMapper.readValue(data, TTSRequest.class);
        String originalTtsId = ttsRequest.getTtsId();
        try {

            String text = ttsRequest.getText();
            // 获取语速参数，如果未提供则使用默认值0f
            Float speed = ttsRequest.getSpeed() != null ? ttsRequest.getSpeed() : 0f;
            MessageSender messageSender = sessionSenders.computeIfAbsent(session, MessageSender::new);

            // 统一按中文限制分割，每600个字符一个片段
            int maxLength = 600;

            List<String> segments = splitText(text, maxLength);
            logger.info("Sent segments message for segments: {}", segments.toString());

            // 发送分段信息到前端
            // 处理文本，移除 SSML 标签
            List<String> processedSegments = segments.stream()
                    .map(str -> str.replaceAll("<[^>]+>", "").replaceAll("[^\\S]", ""))
                    .collect(Collectors.toList());
            Map<String, Object> segmentsInfo = new HashMap<>();
            segmentsInfo.put("total", processedSegments.size());
            segmentsInfo.put("segments", processedSegments);
            segmentsInfo.put("requestId", originalTtsId);
            logger.info("Sent segments message for segments: {}", processedSegments.toString());
            TTSResponse SEGMENTSResponse = new TTSResponse("SEGMENTS", segmentsInfo);
            MessageWrapper segmentMsg = new MessageWrapper("TTS_EVENT", SEGMENTSResponse);
            messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(segmentMsg)));
            // 为每个片段进行语音合成
            for (int i = 0; i < segments.size(); i++) {
                String segmentTtsId = originalTtsId + "_" + i; // 为每个片段生成唯一ttsId
                String segmentText = segments.get(i);
                SpeechSynthesizer synthesizer = null;

                // 创建 TTS 请求
                SpeechSynthesizerRequest request = new SpeechSynthesizerRequest();
                request.setText(segmentText);
                request.setVoiceType(301036);
                request.setVolume(0f);
                request.setSpeed(speed);
                request.setCodec("mp3");
                request.setSampleRate(16000);
                request.setEnableSubtitle(true);
                request.setEmotionCategory("neutral");
                request.setEmotionIntensity(100);
                request.setSessionId(UUID.randomUUID().toString());
                request.set("SegmentRate", 0);

                // 创建 TTS 监听器
                SpeechSynthesizerListener listener = createListener(messageSender, segmentTtsId);

                // 创建凭证
                Credential credential = new Credential(appId, secretId, SecretKey);

                try {


                    synthesizer = new SpeechSynthesizer(proxy, credential, request, listener);
                    activeSynthesizers.put(segmentTtsId, synthesizer); // 存储合成器
                    long startTime = System.currentTimeMillis();
                    synthesizer.start();
                    logger.info("TTS start latency: {} ms", System.currentTimeMillis() - startTime);

                    startTime = System.currentTimeMillis();
                    synthesizer.stop();
                    logger.info("TTS stop latency: {} ms", System.currentTimeMillis() - startTime);

                    activeSynthesizers.remove(segmentTtsId); // 完成后移除
                } finally {
                    if (synthesizer != null) {
                        // 任务完成后移除合成器和消息发送器
                        activeSynthesizers.remove(ttsRequest.getTtsId());
                        synthesizer.close();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error processing TTS request", e);
            try {
                Map<String, Object> errorInfo = new HashMap<>();
                errorInfo.put("error", e.getMessage());
                errorInfo.put("requestId", originalTtsId);
                TTSResponse ttsResponse = new TTSResponse("ERROR", errorInfo);
                MessageWrapper errorMsg = new MessageWrapper("TTS_EVENT", ttsResponse);
                session.sendMessage(new TextMessage(objectMapper.writeValueAsString(errorMsg)));
            } catch (Exception ex) {
                logger.error("Error sending error message", ex);
            }
        }
    }

    // 创建 TTS 监听器
    private SpeechSynthesizerListener createListener(MessageSender messageSender, String ttsId) {
        return new SpeechSynthesizerListener() {
            @Override
            public void onSynthesisStart(SpeechSynthesizerResponse response) {
                try {
                    response.setRequestId(ttsId);
                    TTSResponse ttsResponse = new TTSResponse("START", response);
                    MessageWrapper startMsg = new MessageWrapper("TTS_EVENT", ttsResponse);
                    messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(startMsg)));
                } catch (Exception e) {
                    logger.error("Error sending start message", e);
                }
            }

            @Override
            public void onSynthesisEnd(SpeechSynthesizerResponse response) {
                try {
                    response.setRequestId(ttsId);
                    TTSResponse ttsResponse = new TTSResponse("END", response);
                    MessageWrapper endMsg = new MessageWrapper("TTS_EVENT", ttsResponse);

                    messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(endMsg)));
                } catch (Exception e) {
                    logger.error("Error sending end message", e);
                }
            }

            @Override
            public void onAudioResult(ByteBuffer buffer) {
                try {
                    if (buffer != null && buffer.remaining() > 0) {
                        byte[] ttsIdBytes = ttsId.getBytes(StandardCharsets.UTF_8);
                        byte[] audioData = new byte[buffer.remaining()];
                        buffer.get(audioData);

                        // 创建一个新的ByteBuffer并写入数据
                        ByteBuffer combinedBuffer = ByteBuffer.allocate(4 + ttsIdBytes.length + audioData.length);
                        try {
                            combinedBuffer.putInt(ttsIdBytes.length)
                                    .put(ttsIdBytes)
                                    .put(audioData);
                            combinedBuffer.flip(); // 准备读取

                            messageSender.sendMessage(new BinaryMessage(combinedBuffer));
                            logger.debug("Audio data sent successfully - ttsId: {}, audioLength: {}",
                                    ttsId, audioData.length);
                        } catch (BufferOverflowException e) {
                            logger.error("Buffer overflow while combining audio data - ttsId: {}", ttsId, e);
                        }
                    } else {
                        logger.warn("Received empty audio buffer for ttsId: {}", ttsId);
                    }
                } catch (Exception e) {
                    logger.error("Error processing audio data for ttsId: {}", ttsId, e);
                }
            }

            @Override
            public void onTextResult(SpeechSynthesizerResponse response) {
                try {
                    response.setRequestId(ttsId);
                    TTSResponse ttsResponse = new TTSResponse("TEXT", response);
                    MessageWrapper textMsg = new MessageWrapper("TTS_EVENT", ttsResponse);
                    messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(textMsg)));
                    logger.info("Sent TEXT message for ttsId: {}", ttsId);
                } catch (Exception e) {
                    logger.error("Error sending text result", e);
                }
            }

            @Override
            public void onSynthesisFail(SpeechSynthesizerResponse response) {
                try {
                    response.setRequestId(ttsId);
                    TTSResponse ttsResponse = new TTSResponse("ERROR", response);
                    MessageWrapper errorMsg = new MessageWrapper("TTS_EVENT", ttsResponse);
                    messageSender.sendMessage(new TextMessage(objectMapper.writeValueAsString(errorMsg)));
                    logger.info("Sent ERROR message for ttsId: {}", ttsId);
                } catch (Exception e) {
                    logger.error("Error sending failure message", e);
                }
            }
        };
    }

    private static List<String> splitText(String text, int maxLength) {
        List<String> segments = new ArrayList<>();

        // 定义句子结尾的标点符号
        String[] endPunctuations = {"。", "！", "？", ".", "!", "?"};

        int startIndex = 0;
        while (startIndex < text.length()) {
            // 如果剩余文本长度小于最大长度，直接添加
            if (startIndex + maxLength >= text.length()) {
                segments.add(text.substring(startIndex));
                break;
            }

            // 在最大长度范围内查找分割点
            int endIndex = startIndex + maxLength;

            // 检查是否会切断XML/SSML标签
            boolean inTag = false;
            int tagStartIndex = -1;

            // 检查从起始位置到当前分割点之间是否有未闭合的标签
            for (int i = startIndex; i < endIndex; i++) {
                char c = text.charAt(i);
                if (c == '<' && !inTag) {
                    inTag = true;
                    tagStartIndex = i;
                } else if (c == '>' && inTag) {
                    inTag = false;
                }
            }

            // 如果分割点处于标签内部，调整分割点到标签开始前
            if (inTag && tagStartIndex != -1) {
                endIndex = tagStartIndex;
            } else {
                // 标签检查通过后，尝试在句子末尾分割
                int lastPunctuationIndex = -1;
                for (int i = endIndex; i > startIndex; i--) {
                    String currentChar = text.substring(i - 1, i);
                    for (String punct : endPunctuations) {
                        if (currentChar.equals(punct)) {
                            lastPunctuationIndex = i;
                            break;
                        }
                    }
                    if (lastPunctuationIndex != -1) break;
                }

                if (lastPunctuationIndex != -1) {
                    endIndex = lastPunctuationIndex;
                }
            }

            // 检查结果分割点是否会切断一对完整的XML标签
            String segment = text.substring(startIndex, endIndex);
            int openTags = countOccurrences(segment, '<');
            int closeTags = countOccurrences(segment, '>');

            // 如果标签不平衡，尝试寻找更合适的分割点
            if (openTags != closeTags) {
                // 从后向前寻找最后一个闭合标签位置
                int lastCloseTagPos = segment.lastIndexOf('>');
                if (lastCloseTagPos != -1) {
                    endIndex = startIndex + lastCloseTagPos + 1;
                } else {
                    // 如果没有闭合标签，则尝试在第一个开始标签前分割
                    int firstOpenTagPos = segment.indexOf('<');
                    if (firstOpenTagPos != -1) {
                        endIndex = startIndex + firstOpenTagPos;
                    }
                }
            }

            segments.add(text.substring(startIndex, endIndex));
            startIndex = endIndex;
        }

        return segments;
    }

    // 辅助方法：计算字符在字符串中出现的次数
    private static int countOccurrences(String text, char target) {
        int count = 0;
        for (int i = 0; i < text.length(); i++) {
            if (text.charAt(i) == target) {
                count++;
            }
        }
        return count;
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String userId = (String) session.getAttributes().get("userId");
        if (userId != null) {
            Set<WebSocketSession> sessions = userSessions.get(userId);
            if (sessions != null) {
                sessions.remove(session);
                if (sessions.isEmpty()) {
                    userSessions.remove(userId);
                    userReferenceFaces.remove(userId);
                }
            }

            MessageSender messageSender = sessionSenders.remove(session);
            if (messageSender != null) {
                messageSender.shutdown();
            }

            logger.info("User {} disconnected, session {}", userId, session.getId());
        }
    }

    private String extractToken(WebSocketSession session) {
        // 从WebSocket握手请求中提取token
        return session.getUri().getQuery().replace("token=", "");
    }

    private String validateToken(String token) {
        try {
            Claims user = jwtUtil.parseJWT(token);
            return user.getId();
        } catch (Exception e) {
            logger.error("Invalid token", e);
            return null;
        }
    }

    /**
     * 消息发送器，用于按顺序发送消息
     */
    public static class MessageSender {
        private final WebSocketSession session;
        private final ConcurrentLinkedQueue<Object> messageQueue = new ConcurrentLinkedQueue<>();
        private volatile boolean isSending = false;
        private volatile boolean isStopped = false;
        private final ExecutorService executorService;

        public MessageSender(WebSocketSession session) {
            this.session = session;
            this.executorService = Executors.newSingleThreadExecutor(r -> {
                Thread thread = new Thread(r);
                thread.setName("MessageSender-" + session.getId());
                return thread;
            });
        }

        public void resume() {
            isStopped = false;
        }

        public void stop() {
            isStopped = true;
            messageQueue.clear();
        }

        public void sendMessage(Object message) {
            if (isStopped) {
                return;
            }
            messageQueue.offer(message);
            if (!isSending) {
                processQueue();
            }
        }

        private void processQueue() {
            if (isSending || messageQueue.isEmpty()) {
                return;
            }

            isSending = true;

            executorService.submit(() -> {
                try {

                    while (!messageQueue.isEmpty() && !isStopped) {
                        Object message = messageQueue.poll();
                        if (message == null) continue;
                        if (!session.isOpen()) {
                            logger.warn("Session {} is closed, skipping message", session.getId());
                            break;
                        }
                        try {
                            if (message instanceof BinaryMessage) {
                                session.sendMessage((BinaryMessage) message);

                            } else if (message instanceof TextMessage) {

                                session.sendMessage((TextMessage) message);

                            }
                            Thread.sleep(10); // 添加小延迟，避免发送过快
                        } catch (IOException e) {
                            logger.error("Error sending message: {}", e.getMessage(), e);
                        } catch (Exception e) {
                            logger.error("Unexpected error sending message: {}", e.getMessage(), e);
                        }
                    }
                } finally {
                    isSending = false;
                    if (!messageQueue.isEmpty() && !isStopped) {
                        processQueue();
                    }
                }
            });
        }

        public void shutdown() {
            stop();
            executorService.shutdownNow();
        }
    }
}
