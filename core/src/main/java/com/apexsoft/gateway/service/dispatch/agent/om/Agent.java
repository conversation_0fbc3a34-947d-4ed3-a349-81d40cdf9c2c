package com.apexsoft.gateway.service.dispatch.agent.om;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("agent")
public class Agent implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String agentId;
    private String agentName;
    private String encryptedPassword;
    private int org;
    private String techs;

    // 非数据库字段
    // 坐席状态，1，空闲，2，示忙
    @TableField(exist = false)
    private int status = 1;
}
