package com.apexsoft.gateway.service.coworkscript;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.coworkscript.dao.CoworkScriptMapper;
import com.apexsoft.gateway.service.coworkscript.om.CoworkScript;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 揭示信息服务类
 */
@Service
@Slf4j
public class CoworkScriptService extends ServiceImpl<CoworkScriptMapper, CoworkScript> {

  /**
   * 根据业务代码查询单个脚本配置
   *
   * @param businessCode 业务代码
   * @return 查询结果
   */
  public CommonResponse getCoworkScript(String businessCode) {
    CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
    try {
      CoworkScript coworkScript = getById(businessCode);
      JSONObject data = new JSONObject();
      data.put("coworkScript", coworkScript);
      response.setData(data);
    } catch (Exception e) {
      log.error("查询揭示信息失败", e);
      response.setCode(JSONResponse.CODE_FAIL);
      response.setNote("查询失败：" + e.getMessage());
    }
    return response;
  }

  /**
   * 获取揭示信息列表（分页）
   *
   * @param businessCode    业务代码
   * @param initiateChannel 发起渠道
   * @param pageNumber      页码
   * @param pageSize        每页大小
   * @return 脚本列表
   */
  public CommonResponse getCoworkScriptList(String businessCode, String initiateChannel,
      Integer pageNumber, Integer pageSize) {
    CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
    try {
      QueryWrapper<CoworkScript> queryWrapper = new QueryWrapper<>();

      // 添加查询条件
      if (StringUtils.isNotBlank(businessCode)) {
        queryWrapper.eq("business_code", businessCode);
      }
      if (StringUtils.isNotBlank(initiateChannel)) {
        queryWrapper.eq("initiate_channel", initiateChannel);
      }

      // 按业务代码排序
      queryWrapper.orderByAsc("business_code");

      Page<CoworkScript> page = new Page<>(pageNumber, pageSize);
      Page<CoworkScript> scriptPage = page(page, queryWrapper);

      response.setData(JSONObject.parseObject(JSONObject.toJSONString(scriptPage)));
    } catch (Exception e) {
      log.error("获取揭示信息列表失败", e);
      response.setCode(JSONResponse.CODE_FAIL);
      response.setNote("获取揭示信息列表失败：" + e.getMessage());
    }
    return response;
  }

  /**
   * 新增揭示信息配置
   *
   * @param params 参数
   * @return 操作结果
   */
  public CommonResponse addCoworkScript(JSONObject params) {
    CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "添加成功");
    try {
      CoworkScript coworkScript = new CoworkScript();

      // 设置业务代码
      if (params.getString("businessCode") != null &&
          !StringUtils.isBlank(params.getString("businessCode"))) {
        coworkScript.setBusinessCode(params.getString("businessCode"));
      }

      // 设置发起渠道
      if (params.getString("initiateChannel") != null &&
          !StringUtils.isBlank(params.getString("initiateChannel"))) {
        coworkScript.setInitiateChannel(params.getString("initiateChannel"));
      }

      // 设置话术内容
      if (params.getString("script") != null &&
          !StringUtils.isBlank(params.getString("script"))) {
        coworkScript.setScript(params.getString("script"));
      }

      boolean result = save(coworkScript);
      if (!result) {
        response.setCode(JSONResponse.CODE_FAIL);
        response.setNote("添加失败");
      }
    } catch (Exception e) {
      log.error("添加揭示信息失败", e);
      response.setCode(JSONResponse.CODE_FAIL);
      response.setNote("添加失败：" + e.getMessage());
    }
    return response;
  }

  /**
   * 删除揭示信息配置
   *
   * @param businessCode 业务代码
   * @return 操作结果
   */
  public CommonResponse deleteCoworkScript(String businessCode) {
    CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "删除成功");
    try {
      boolean result = removeById(businessCode);
      if (!result) {
        response.setCode(JSONResponse.CODE_FAIL);
        response.setNote("删除失败，可能记录不存在");
      }
    } catch (Exception e) {
      log.error("删除揭示信息失败", e);
      response.setCode(JSONResponse.CODE_FAIL);
      response.setNote("删除失败：" + e.getMessage());
    }
    return response;
  }

  /**
   * 更新揭示信息配置
   *
   * @param params 参数
   * @return 操作结果
   */
  public CommonResponse updateCoworkScript(JSONObject params) {
    CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "更新成功");
    try {
      CoworkScript coworkScript = new CoworkScript();

      // 设置业务代码（作为更新条件）
      if (params.getString("businessCode") != null &&
          !StringUtils.isBlank(params.getString("businessCode"))) {
        coworkScript.setBusinessCode(params.getString("businessCode"));
      }

      // 设置发起渠道
      if (params.getString("initiateChannel") != null &&
          !StringUtils.isBlank(params.getString("initiateChannel"))) {
        coworkScript.setInitiateChannel(params.getString("initiateChannel"));
      }

      // 设置话术内容
      if (params.getString("script") != null &&
          !StringUtils.isBlank(params.getString("script"))) {
        coworkScript.setScript(params.getString("script"));
      }

      boolean result = updateById(coworkScript);
      if (!result) {
        response.setCode(JSONResponse.CODE_FAIL);
        response.setNote("更新失败，可能记录不存在");
      }
    } catch (Exception e) {
      log.error("更新揭示信息失败", e);
      response.setCode(JSONResponse.CODE_FAIL);
      response.setNote("更新失败：" + e.getMessage());
    }
    return response;
  }
}