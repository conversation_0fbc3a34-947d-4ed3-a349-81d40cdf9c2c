package com.apexsoft.gateway.service.socket;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import com.google.gson.Gson;
import com.tencent.asr.service.SpeechRecognizer;
import com.tencent.core.utils.ByteUtils;
import com.tencent.core.ws.Credential;
import com.tencent.core.ws.SpeechClient;
import com.tencent.tts.utils.Ttsutils;
import com.tencent.ttsv2.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.io.File;
import java.io.FileInputStream;
import java.nio.ByteBuffer;
import java.security.Principal;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

public class TTSController {

    @Value("${tc-ai.usersign-expire}")
    private long expire;
    @Value("${tc-ai.SecretId}")
    private String secretId;
    @Value("${tc-ai.SecretKey}")
    private String SecretKey;
    @Value("${tc-ai.appid}")
    private String appId;
    private static final Logger logger = LoggerFactory.getLogger(TTSController.class);
    private final SimpMessagingTemplate messagingTemplate;

    // Singleton SpeechClient
    private static final SpeechClient proxy;

    static {
        try {
            proxy = new SpeechClient(TtsConstant.DEFAULT_TTS_REQ_URL);
        } catch (Exception e) {
            logger.error("Failed to initialize SpeechClient: {}", e.getMessage());
            throw new RuntimeException("SpeechClient initialization failed", e);
        }
    }

    public TTSController(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
    }

    public void handleTTSRequest(TTSRequest message, SimpMessageHeaderAccessor headerAccessor, Principal principal) {
        // Extract user and text from request
        String text = message.getText();
        String sessionId = UUID.randomUUID().toString();
        String recipient = principal.getName();
        processTTS(text, recipient, sessionId);

    }

    private void processTTS(String text, String recipient, String sessionId) {
        // Replace with your Tencent Cloud credentials

        Credential credential = new Credential(appId, secretId, SecretKey);

        SpeechSynthesizerRequest request = new SpeechSynthesizerRequest();
        request.setText(text);
        request.setVoiceType(301036);
        request.setVolume(0f);
        request.setSpeed(0f);
        request.setCodec("mp3");
        request.setSampleRate(16000);
        request.setEnableSubtitle(true);
        request.setEmotionCategory("happy");
        request.setEmotionIntensity(100);
        request.setSessionId(UUID.randomUUID().toString());//sessionId，需要保持全局唯一（推荐使用 uuid），遇到问题需要提供该值方便服务端排查
        request.set("SegmentRate", 0); //sdk暂未支持参数，可通过该方法设置
        logger.debug("session_id:{}", request.getSessionId());
        SpeechSynthesizerListener listener = new SpeechSynthesizerListener() {//tips：回调方法中应该避免进行耗时操作，如果有耗时操作建议进行异步处理否则会影响websocket请求处理
            byte[] audio = new byte[0];

            @Override
            public void onSynthesisStart(SpeechSynthesizerResponse response) {
                logger.info("{} session_id:{},{}", "onSynthesisStart", response.getSessionId(), new Gson().toJson(response));
            }

            @Override
            public void onSynthesisEnd(SpeechSynthesizerResponse response) {
                logger.info("{} session_id:{},{}", "onSynthesisEnd", response.getSessionId(), new Gson().toJson(response));
//                if ("pcm".equals(request.getCodec())) {
//                    Ttsutils.responsePcm2Wav(16000, audio, request.getSessionId());
//                }
//                if ("mp3".equals(request.getCodec())) {
//                    Ttsutils.saveResponseToFile(audio, "./" + request.getSessionId() + ".mp3");
//                }
                JSONObject res=new JSONObject();
                res.put("status", "COMPLETED");

                messagingTemplate.convertAndSendToUser(recipient, "/queue/audio", res.toString());
            }

            @Override
            public void onAudioResult(ByteBuffer buffer) {

                int remaining = buffer.remaining();
                if (remaining > 0) {
                    byte[] audioData = new byte[remaining];
                    buffer.get(audioData);

                    // Log to check if data is being read correctly
                    logger.info("Audio data size: {}", audioData.length);
                    String base64AudioData = Base64.getEncoder().encodeToString(audioData);
                    JSONObject res=new JSONObject();
                    res.put("status", "audio");
                    res.put("result", base64AudioData);
                    // Send audio data to the user who made the request
                    messagingTemplate.convertAndSendToUser(recipient, "/queue/audio", res.toString());
                } else {
                    logger.warn("No audio data available in buffer");
                }
            }

            @Override
            public void onTextResult(SpeechSynthesizerResponse response) {
                logger.info("{} session_id:{},{}", "onTextResult", response.getSessionId(), new Gson().toJson(response));
                JSONObject res=new JSONObject();
                res.put("status", "textResult");
                res.put("result", new Gson().toJson(response));
                messagingTemplate.convertAndSendToUser(recipient, "/queue/audio",res.toString() );
            }

            @Override
            public void onSynthesisFail(SpeechSynthesizerResponse response) {
                logger.info("{} session_id:{},{}", "onSynthesisFail", response.getSessionId(), new Gson().toJson(response));
                JSONObject res=new JSONObject();
                res.put("status", "ERROR");
                messagingTemplate.convertAndSendToUser(recipient, "/queue/audio", res.toString());
            }
        };

        //synthesizer不可重复使用，每次合成需要重新生成新对象
        SpeechSynthesizer synthesizer = null;
        try {
            synthesizer = new SpeechSynthesizer(proxy, credential, request, listener);
            long currentTimeMillis = System.currentTimeMillis();
            synthesizer.start();
            logger.info("synthesizer start latency : " + (System.currentTimeMillis() - currentTimeMillis) + " ms");
            currentTimeMillis = System.currentTimeMillis();
            synthesizer.stop();
            logger.info("synthesizer stop latency : " + (System.currentTimeMillis() - currentTimeMillis) + " ms");

        } catch (Exception e) {
            logger.error(e.getMessage());
        } finally {
            if (synthesizer != null) {
                synthesizer.close(); //关闭连接
            }
        }
    }
}
