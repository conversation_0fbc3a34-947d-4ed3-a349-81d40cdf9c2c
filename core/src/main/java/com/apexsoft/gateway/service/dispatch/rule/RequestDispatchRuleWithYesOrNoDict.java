package com.apexsoft.gateway.service.dispatch.rule;

import com.apexsoft.dispatcher.dispatch.rule.RequestDispatchRule;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;

import java.io.Serializable;
import java.util.List;

/**
 * 所有使用1|是;0|否字典的派单规则抽象父类，是和否的规则已经实现，具体规则子类只需要在execPushCompare中实现真正的派单规则即可
 */
public abstract class RequestDispatchRuleWithYesOrNoDict extends RequestDispatchRule implements Serializable, Constant {
    private boolean isOpen = false;

    @Override
    public void init() throws Exception {
        super.init();
        String ruleValue = dispatchRule.getDispatchRuleConfigEntity().getRuleValue();
        isOpen = YES.equals(ruleValue);
    }

    @Override
    public int pushCompare(QueueWitnessRequest o1, QueueWitnessRequest o2, List<QueueWitnessRequest> queueWitnessRequests) {
        if (!isOpen) {
            return 0;
        }
        return execPushCompare(o1, o2, queueWitnessRequests);
    }

    protected abstract int execPushCompare (QueueWitnessRequest o1, QueueWitnessRequest o2, List<QueueWitnessRequest> queueWitnessRequests);

}
