package com.apexsoft.gateway.service.dispatch.cuslevel.dao;

import com.apexsoft.gateway.service.dispatch.cuslevel.om.CusLevel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CusLevelMapper extends BaseMapper<CusLevel> {

    @Select("SELECT cus_level, score FROM customer_level_config")
    List<CusLevel> getAll ();
}
