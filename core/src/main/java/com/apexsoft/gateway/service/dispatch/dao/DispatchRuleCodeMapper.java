package com.apexsoft.gateway.service.dispatch.dao;

import com.apexsoft.dispatcher.om.DispatchRuleConfigEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DispatchRuleCodeMapper extends BaseMapper<DispatchRuleConfigEntity> {

    @Select("select " +
            "a.rule_code, " +
            "a.rule_name, " +
            "a.priority, " +
            "a.rule_dict_type, " +
            "a.rule_dict_key, " +
            "a.rule_type, " +
            "a.html_element_type, " +
            "a.state, " +
            "a.customize_sort_enable, " +
            "a.default_rule, " +
            "b.rule_value " +
            "from dispatch_rule_code a left join dispatch_rule_value b " +
            "on a.rule_code=b.rule_code order by rule_type,priority")
    List<DispatchRuleConfigEntity> getAll() throws Exception;
}
