package com.apexsoft.gateway.service.dispatch.cuslevel;

import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.service.dispatch.AbstractManager;
import com.apexsoft.gateway.service.dispatch.cuslevel.dao.CusLevelMapper;
import com.apexsoft.gateway.service.dispatch.cuslevel.om.CusLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

// @todo AbstractManager的抽象方法还没完全实现
@Service
@Slf4j
public class CusLevelManager extends AbstractManager<Integer, CusLevel> {

    @Autowired
    private CusLevelMapper cusLevelMapper;

    @Override
    protected String name() {
        return "cusLevel";
    }

    @Override
    protected List<CusLevel> getAll() {
        return cusLevelMapper.getAll();
    }

    @Override
    protected Integer getKey(CusLevel cusLevel) {
        return cusLevel.getCusLevel();
    }

    public Integer getCusLevelScore (Integer cusLevel) {
        return getCusLevelScore(cusLevel, 0);
    }

    public Integer getCusLevelScore (Integer cusLevel, int defaultValue) {
        return allElements.get(cusLevel) == null ? defaultValue : allElements.get(cusLevel).getScore();
    }

    @Override
    protected CommonResponse insertOne(CusLevel cusLevel) {
        return null;
    }

    @Override
    protected CommonResponse delete(String... keys) {
        return null;
    }

    @Override
    protected CommonResponse updateOne(CusLevel cusLevel) {
        return null;
    }
}
