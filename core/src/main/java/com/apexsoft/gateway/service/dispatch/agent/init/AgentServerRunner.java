package com.apexsoft.gateway.service.dispatch.agent.init;

import com.corundumstudio.socketio.SocketIOServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;

/**
 * <p>
 * websocket服务器启动
 * </p>
 *
 * @package: com.xkcoding.websocket.socketio.init
 * @description: websocket服务器启动
 * @author: yangkai.shen
 * @date: Created in 2018-12-18 17:07
 * @copyright: Copyright (c) 2018
 * @version: V1.0
 * @modified: yangkai.shen
 */
@Component
@Slf4j
public class AgentServerRunner implements CommandLineRunner {

    @Autowired
    @Qualifier("agentSocketServer")
    private SocketIOServer agentServer;

    @Override
    public void run(String... args) {
//        if(UsePhoneSign) {
        agentServer.start();
        log.info("witness agent websocket 服务器启动成功。。。");
//        }else{
//            logger.info("websocket UsePhoneSign:false,不启动服务");
//        }
    }
//    / 在应用关闭时执行的方法
    @PreDestroy
    public void onApplicationShutdown() {
        log.info("Closing agent agent WebSocket connections...");
        // 在这里执行关闭 WebSocket 连接的代码
        agentServer.stop();
        log.info("agent agent WebSocket connections closed.");
    }
}
