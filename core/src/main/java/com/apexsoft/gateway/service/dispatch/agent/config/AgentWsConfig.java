package com.apexsoft.gateway.service.dispatch.agent.config;

import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.corundumstudio.socketio.SocketConfig;
import com.corundumstudio.socketio.SocketIOServer;
import com.corundumstudio.socketio.annotation.SpringAnnotationScanner;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * websocket服务器配置
 * </p>
 *
 * @package: com.xkcoding.websocket.socketio.config
 * @description: websocket服务器配置
 * @author: yangkai.shen
 * @date: Created in 2018-12-18 16:42
 * @copyright: Copyright (c) 2018
 * @version: V1.0
 * @modified: yangkai.shen
 */
@Configuration
@EnableConfigurationProperties({AgentWsProperties.class})
@Slf4j
public class AgentWsConfig {

    @Autowired
    private JwtUtil jwtUtil;

    @Bean("agentSocketServer")
    public SocketIOServer server(AgentWsProperties wsConfig) {
        com.corundumstudio.socketio.Configuration config =
                new com.corundumstudio.socketio.Configuration();
//        config.setHostname(wsConfig.getHost());
        SocketConfig socketConfig = new SocketConfig();
        socketConfig.setReuseAddress(true);
        config.setSocketConfig(socketConfig);
        if(wsConfig.getPort() != null){
            config.setPort(wsConfig.getPort());
        }

//        config.setPingTimeout(60000);  // 默认值 60 秒
//        config.setPingInterval(25000); // 默认值 25 秒
//        config.setPort(8090);
//        config.setHostname("0.0.0.0");
//        config.setAllowCustomRequests(true);
//        config.setContext(wsConfig.getContext());
        //这个listener可以用来进行身份验证
        config.setAuthorizationListener(data -> {
            // http://localhost:8081?token=xxxxxxx

            String token = data.getSingleUrlParam("token");

            try {
                Claims user=jwtUtil.parseJWT(token);
                return true;
            }catch (Exception e){
                return false;
            }

        });

        return new SocketIOServer(config);
    }


    /**
     * Spring 扫描自定义注解
     */
    @Bean("agentSpringAnnotationScanner")
    public SpringAnnotationScanner springAnnotationScanner(SocketIOServer server) {
        return new SpringAnnotationScanner(server);
    }
}
