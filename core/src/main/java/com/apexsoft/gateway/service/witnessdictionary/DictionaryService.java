package com.apexsoft.gateway.service.witnessdictionary;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.dispatch.bizcode.dao.BizCodeMapper;
import com.apexsoft.gateway.service.dispatch.bizcode.om.BizCode;
import com.apexsoft.gateway.service.dispatch.bizlevel.dao.BizLevelMapper;
import com.apexsoft.gateway.service.dispatch.bizlevel.om.BizLevel;
import com.apexsoft.gateway.service.dispatch.cuslevel.dao.CusLevelMapper;
import com.apexsoft.gateway.service.dispatch.cuslevel.om.CusLevel;
import com.apexsoft.gateway.service.witnessdictionary.dao.WitnessDictionaryMapper;
import com.apexsoft.gateway.service.witnessdictionary.om.WitnessDictionary;
import com.apexsoft.gateway.service.witnessscript.om.WitnessScriptConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

@Service
@Slf4j
public class DictionaryService extends ServiceImpl<WitnessDictionaryMapper, WitnessDictionary>
        implements IService<WitnessDictionary> {

    @Autowired
    WitnessDictionaryMapper witnessDictionaryMapper;

    @Autowired
    private BizLevelMapper bizLevelMapper;

    @Autowired
    private BizCodeMapper bizCodeMapper;

    @Autowired
    private CusLevelMapper cusLevelMapper;

    public CommonResponse getWitnessDict(String dicCode, String[] dicCodes, String[] tableNames) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try {
            List<WitnessDictionary> dictList;
            JSONObject data = new JSONObject();
            if (dicCodes != null && dicCodes.length > 0) {
                // 使用 MyBatis-Plus 的查询构造器来处理 IN 查询
                QueryWrapper<WitnessDictionary> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("dic_code", (Object[]) dicCodes);
                dictList = this.list(queryWrapper);
            } else if (dicCode != null && !dicCode.isEmpty()) {
                // 单个字典代码查询
                QueryWrapper<WitnessDictionary> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("dic_code", dicCode);
                dictList = this.list(queryWrapper);
            } else {
                dictList = null;
            }
            data.put("dict", dictList);

            if( tableNames != null && tableNames.length > 0) {
                Arrays.stream(tableNames).forEach(tableName -> getTableList(data, tableName));
            }

            response.setData(data);
        } catch (Exception e) {
            log.error("查询字典数据失败", e);
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("查询字典数据失败: " + e.getMessage());
        }
        return response;
    }

    public void getTableList(JSONObject data,String tableName) {
        if("business_code".equals(tableName)){
            List<BizCode> bizCodeList = bizCodeMapper.getAll();
            data.put("bizCodeList", bizCodeList);
        }else if("business_level_config".equals(tableName)){
            List<BizLevel> bizLevelList = bizLevelMapper.getAll();
            data.put("bizLevelList", bizLevelList);
        }else if("customer_level_config".equals(tableName)){
            List<CusLevel> cusLevelList = cusLevelMapper.getAll();
            data.put("cusLevelList", cusLevelList);
        }
    }

    public CommonResponse getWitnessDictList(String dicCode, int current, int size) {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try {
            QueryWrapper<WitnessDictionary> queryWrapper = new QueryWrapper<>();
            if (dicCode != null && !dicCode.isEmpty()) {
                queryWrapper.eq("dic_code", dicCode);
            }
            Page<WitnessDictionary> page = new Page<>(current, size);
            // 使用继承自ServiceImpl的page方法
            Page<WitnessDictionary> dictPage = page(page, queryWrapper);
            // JSONObject data = new JSONObject();
            // data.put("list", dictPage.getRecords());
            // data.put("total", dictPage.getTotal());
            // response.setData(data);
            response.setData(JSON.parseObject(JSON.toJSONString(dictPage)));
        } catch (Exception e) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote(e.toString());
        }
        return response;
    }
}
