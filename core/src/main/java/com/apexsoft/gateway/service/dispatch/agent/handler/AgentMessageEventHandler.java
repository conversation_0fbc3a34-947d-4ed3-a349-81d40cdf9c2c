package com.apexsoft.gateway.service.dispatch.agent.handler;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.apexsoft.gateway.service.dispatch.agent.AgentService;
import com.apexsoft.gateway.service.dispatch.agent.config.AgentEvents;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.UUID;

@Component
@Slf4j
public class AgentMessageEventHandler {

    @Autowired
    @Qualifier("agentSocketServer")
    private SocketIOServer agentSocketServer;

    @Autowired
    private AgentService agentService;

    @Autowired
    private JwtUtil jwtUtil;

    private String getUserId(SocketIOClient client) {
        String token = client.getHandshakeData().getSingleUrlParam("token");
        return jwtUtil.parseJWT(token).getId();
    }

    @PostConstruct
    public void init() {
        log.debug("SocketEventListener initialized");

        //添加监听，客户端自动连接到 socket 服务端
        agentSocketServer.addConnectListener(client -> {
            String userId = getUserId(client);
            UUID sessionId = client.getSessionId();

//            dbTemplate.save(userId, sessionId);
            agentService.agentOnLine(userId, sessionId.toString());
            log.info("连接成功,【userId】= {},【sessionId】= {}", userId, sessionId);
        });

        //添加监听，客户端跟 socket 服务端自动断开
        agentSocketServer.addDisconnectListener(client -> {
            String userId = getUserId(client);
            UUID sessionId = client.getSessionId();

//            dbTemplate.deleteByUserId(userId);
            agentService.agentOffLine(userId);

            log.info("客户端断开连接,【userId】= {},【sessionId】= {}", userId, sessionId);
            client.disconnect();
        });
        // 接受事件
        agentSocketServer.addEventListener(AgentEvents.accept.getEventName(), Object.class,
                (client, object, ackRequest) ->
                        ackRequest.sendAckData(agentService.accept(getUserId(client))));
        // 拒绝事件
        agentSocketServer.addEventListener(AgentEvents.reject.getEventName(), Object.class,
                (client, object, ackRequest) -> agentService.reject(getUserId(client)));
        // 见证结果事件
        agentSocketServer.addEventListener(AgentEvents.result.getEventName(), JSONObject.class,
                ((client, jsonObject, ackRequest) -> {
                    String result = jsonObject.getString("result");
                    String reason = jsonObject.getString("reason");
                    agentService.endServe(getUserId(client), result, reason);
                }));

//        agentServer.addListeners();
    }
}
