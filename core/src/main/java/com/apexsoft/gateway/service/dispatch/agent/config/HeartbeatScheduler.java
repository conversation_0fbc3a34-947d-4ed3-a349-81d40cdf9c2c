package com.apexsoft.gateway.service.dispatch.agent.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import javax.servlet.http.HttpSession;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class HeartbeatScheduler {

    private final Map<String, HttpSession> sessionMap = new ConcurrentHashMap<>();

    public void registerSession(WebSocketSession session, HttpSession httpSession) {
        sessionMap.put(session.getId(), httpSession);
    }

    public void unregisterSession(WebSocketSession session) {
        sessionMap.remove(session.getId());
    }

    @Scheduled(fixedRate = 5 * 60 * 1000) // 每 5 分钟执行一次
    public void refreshSessions() {
        for (HttpSession httpSession : sessionMap.values()) {
            if (httpSession != null) {
                // 访问 HttpSession，防止超时
                log.info("heartbeat[{}]", httpSession);
                httpSession.setAttribute("heartbeat", System.currentTimeMillis());
            }
        }
    }
}
