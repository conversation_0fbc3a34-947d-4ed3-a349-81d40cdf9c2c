package com.apexsoft.gateway.service.dispatch.agenttech.om;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
@ApiModel(value = "技能对象", description = "技能对象")
@TableName("agent_tech")
public class AgentTech implements Serializable {
    @ApiModelProperty(value = "id", name = "id", required = true, example = "1")
    @TableId
    private long id;
    @ApiModelProperty(value = "技能组编号", name = "techCode", example = "1001")
    private String techCode;
    @ApiModelProperty(value = "技能组名称", name = "techName", example = "开户办理组")
    private String techName;
    @ApiModelProperty(value = "技能组类别", name = "techType", example = "开户技能组")
    private Integer techType;
    @ApiModelProperty(value = "限办业务等级，多个用;隔开", name = "bizLevel", example = "普通")
    private String bizLevel;
    @ApiModelProperty(value = "限办客户等级，多个用;隔开", name = "cusLevel", example = "普通卡客户")
    private String cusLevel;

    @Override
    public String toString() {
        return "AgentTech{" +
                "id=" + id +
                ", techCode='" + techCode + '\'' +
                ", techName='" + techName + '\'' +
                ", techType=" + techType +
                ", bizLevel='" + bizLevel + '\'' +
                ", cusLevel='" + cusLevel + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
//        return super.equals(obj);
        if (obj instanceof AgentTech) {
            AgentTech agentTech = (AgentTech) obj;
            return agentTech.getTechCode().equals(this.getTechCode());
        }
        return false;
    }
}
