package com.apexsoft.gateway.service.dispatch.agent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.dispatcher.AgentSessionService;
import com.apexsoft.dispatcher.dispatch.Queue;
import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.common.utils.Constant;
//import com.apexsoft.gateway.dao.UserDao;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.dispatch.WitnessUtilService;
import com.apexsoft.gateway.service.dispatch.agent.config.AgentEvents;
import com.apexsoft.gateway.service.dispatch.agent.dao.AgentMapper;
import com.apexsoft.gateway.service.dispatch.agent.handler.AgentWebSocketHandler;
import com.apexsoft.gateway.service.dispatch.agent.om.Agent;
import com.apexsoft.gateway.service.dispatch.agent.om.AgentDetail;
import com.apexsoft.gateway.service.dispatch.agent.om.Message;
import com.apexsoft.gateway.service.dispatch.agenttech.AgentTechManager;
import com.apexsoft.gateway.service.dispatch.agenttech.dao.AgentTechMapper;
import com.apexsoft.gateway.service.dispatch.agenttech.om.AgentTech;
import com.apexsoft.gateway.service.dispatch.client.ClientService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AgentService extends ServiceImpl<AgentMapper, Agent> implements IService<Agent>, Constant {

    @Autowired
    private AgentMapper agentMapper;

    @Autowired
    private AgentSessionService agentSessionService;

    @Autowired
    private WitnessUtilService witnessUtilService;

    @Autowired
    private ClientService clientService;

    @Autowired
    @Qualifier("agentSocketServer")
    private SocketIOServer agentServer;

    @Autowired
    private AgentWebSocketHandler agentWebSocketHandler;

    @Autowired
    private AgentTechManager agentTechManager;

    // @Autowired
    // UserDao userDao;

    @Autowired
    private Queue queue;

    public String agentOnLine(String userId, String sessionId) {
        // 从数据库查询，如果没有信息，则查esb，esb也没有或者查到的柜员属性不是见证人，表示坐席不存在
        Agent agentInfo = agentMapper.selectByAgentId(userId);
        String note = "";
        if (agentInfo == null) {
            // JSONObject result = userDao.queryUser(userId);
            // if (result.getIntValue("code")!=1){
            // note = "查询[" + userId + "]坐席信息失败：" + result.getString("note");
            // log.error(note);
            // return note;
            // }
            // JSONObject userInfo = result.getJSONArray("records").getJSONObject(0);
            // if (!WITNESS_GYSX.equals(userInfo.getString("gysx"))) {
            // note = "用户[" + userId + "]不是视频见证人[gysx=" + userInfo.getString("gysx") + "]";
            // log.error(note);
            // return note;
            // }
            // 新的用户肯定是没有技能组的，所以不能做业务
            // note = "坐席[" + userId + "]无技能组，暂无法服务客户";
            note = "该坐席不存在或不是视频见证人";
            log.error(note);
            return note;
        }
        if (!StringUtils.hasLength(agentInfo.getTechs())) {
            note = "坐席[" + agentInfo.getAgentId() + "]无技能组，请检查配置";
            log.error(note);
            return note;
        }
        agentInfo.setStatus(AGENT_STATUS_FREE);
        AgentSession<Agent> agentSession = new AgentSession<>(userId, sessionId, agentInfo);
        agentSessionService.addAgentSession(agentSession);
        return note;
    }

    public JSONResponse changeStatus(String agentId, String statusStr) {
        JSONResponse response = new JSONResponse();
        AgentSession<Agent> agentSession = agentSessionService.findByAgentId(agentId);
        if (agentSession == null) {
            response.setCode(-1);
            response.setNote("坐席已掉线");
            agentOffLine(agentId);
            return response;
        }
        int status;
        try {
            status = Integer.parseInt(statusStr);
        } catch (Exception e) {
            response.setCode(-2);
            response.setNote("坐席状态值不正确[" + statusStr + "]");
            agentOffLine(agentId);
            return response;
        }
        agentSession.getAgentInfo().setStatus(status);
        agentSessionService.updateAgentSession(agentSession);
        return response;
    }

    public void agentOffLine(String agentId) {
        // 坐席掉线，删除坐席会话，并更新请求表的状态
        agentSessionService.removeAgentSession(agentId);
        clientService.agentOffline(agentId);
    }

    private void sendMsg(String agentId, AgentEvents agentEvent, JSONObject msg) {
        try {
            AgentSession<Agent> agentSession = agentSessionService.findByAgentId(agentId);
            // agentServer.getClient(UUID.fromString(agentSession.getSocketClientSessionId()))
            // .sendEvent(agentEvent.getEventName(), msg);
            if (agentSession != null && agentSession.getSocketClientSessionId() != null) {
                UUID socketIoUuid = UUID.fromString(agentSession.getSocketClientSessionId());
                SocketIOClient client = agentServer.getClient(socketIoUuid);
                if (client != null) {
                    client.sendEvent(agentEvent.getEventName(), msg);
                    return;
                }
            }
            Message message = new Message(agentEvent.getEventName(), msg);
            agentWebSocketHandler.sendMsg(agentId, message);
        } catch (Exception e) {
            log.error("send msg error", e);
        }
    }

    public void sendQueueCountToAllAgent(int count) {
        JSONObject queueState = new JSONObject();
        queueState.put("count", count);
        Collection<AgentSession> agentSessions = agentSessionService.findAllAgentSession();
        agentSessions.forEach(agentSession -> sendMsg(agentSession.getAgentId(), AgentEvents.queueChange, queueState));
    }

    /**
     * 给坐席发送呼叫（通过队列轮询线程去发送）
     *
     * @param agentId 目标坐席id
     * @param info    发送的信息
     */
    public void sendCallInfo(String agentId, JSONObject info) {
        sendMsg(agentId, AgentEvents.call, info);
    }

    /**
     * 给坐席发送呼叫终止
     *
     * @param agentId 目标坐席id
     */
    public void sendCallEnd(String agentId) {
        sendMsg(agentId, AgentEvents.endCall, new JSONObject());
    }

    /**
     * 坐席发送接收请求
     *
     * @param agentId
     */
    public JSONResponse accept(String agentId) {
        JSONResponse response = new JSONResponse(-1, "该坐席未在呼叫状态或呼叫已超时");
        // 接受请求
        QueueWitnessRequest witnessRequest = queue.getRequestByCallAgentId(agentId);
        if (witnessRequest != null) {
            witnessRequest.response(true);
            response.setCode(1);
            response.setNote("接受请求成功");
        }
        return response;
    }

    /**
     * 坐席发送拒绝请求
     *
     * @param agentId
     */
    public void reject(String agentId) {
        // 拒绝请求
        QueueWitnessRequest witnessRequest = queue.getRequestByCallAgentId(agentId);
        if (witnessRequest != null) {
            witnessRequest.response(false);
        }
    }

    /**
     * 结束见证服务，此时坐席将发送见证结果
     * 因为见证服务开始的时候，请求还在队列中，所以通过队列来处理见证开始的事件。
     * 见证服务结束的时候，请求已不在队列中，所以直接响应坐席的结束见证请求即可（即不通过队列线程来处理见证结束的事件）
     *
     * @param agentId 坐席id
     * @param result  见证结果是否通过
     * @param reason  见证结果详细原因
     */
    public void endServe(String agentId, String result, String reason) {
        // 更新表格数据
        AgentSession agentSession = agentSessionService.findByAgentId(agentId);
        clientService.witnessResult(agentSession.getServeRequestId(), result, reason);
        // 更新坐席状态
        agentSessionService.endServe(agentId);
    }

    // 管理接口
    // 获取坐席信息
    public CommonResponse queryAgent(
            String userId,
            String userName,
            String org,
            String techCode,
            String techName,
            int pageNumber, int pageSize) {
        QueryWrapper<Agent> queryWrapper = new QueryWrapper<>();
        witnessUtilService.likeWrapper(queryWrapper, "agent_id", userId);
        witnessUtilService.likeWrapper(queryWrapper, "agent_name", userName);
        // likeWrapper(queryWrapper, "org", org);
        witnessUtilService.likeWrapper(queryWrapper, "techs", techCode);
        if (StringUtils.hasLength(org)) {
            String[] orgs = org.split(";");
            queryWrapper.in("org", (Object) orgs);
        }
        if (StringUtils.hasLength(techName)) {
            Map<String, AgentTech> allElements = agentTechManager.getAllElements();
            List<String> techCodes = allElements.values()
                    .stream()
                    .filter(agentTech -> agentTech.getTechName().contains(techName))
                    .map(AgentTech::getTechCode)
                    .collect(Collectors.toList());

            // IN 条件
            queryWrapper.in("techs", techCodes);
        }
        CommonResponse result = new CommonResponse();
        if (pageSize < 0) {
            // 表示查全部
            List<Agent> agentList = list(queryWrapper);
            result.setData(JSON.parseArray(JSON.toJSONString(agentList)));
        } else {
            // 创建分页对象，设置当前页和每页显示的数量
            Page<Agent> page = new Page<>(pageNumber, pageSize);
            Page<Agent> pageResult = page(page, queryWrapper);
            Map<String, AgentTech> allElements = agentTechManager.getAllElements();
            List<AgentDetail> agentOut = pageResult.getRecords().stream()
                    .map(agent -> {
                        AgentDetail detail = new AgentDetail();
                        // 处理多个技能代码的情况
                        String techNames = "";
                        if (StringUtils.hasLength(agent.getTechs())) {
                            String[] techCodeArray = agent.getTechs().split(";");
                            List<String> techNameList = new ArrayList<>();
                            for (String agentTechCode : techCodeArray) {
                                AgentTech tech = allElements.get(agentTechCode);
                                if (tech != null) {
                                    techNameList.add(tech.getTechName());
                                }
                            }
                            techNames = String.join(";", techNameList);
                        }

                        detail.setTechName(techNames);
                        detail.setAgentId(agent.getAgentId());
                        detail.setAgentName(agent.getAgentName());
                        detail.setId(agent.getId());
                        detail.setOrg(agent.getOrg());
                        detail.setEncryptedPassword(agent.getEncryptedPassword());
                        detail.setTechs(agent.getTechs());
                        return detail;
                    })
                    .collect(Collectors.toList());
            pageResult.setRecords((List<Agent>) (List<?>) agentOut);
            result.setData(JSON.parseObject(JSON.toJSONString(pageResult)));
        }
        return result;
    }

    // private void likeWrapper (QueryWrapper<Agent> queryWrapper, String key,
    // String value) {
    // if (StringUtils.hasLength(value)) {
    // // LIKE 条件
    // queryWrapper.like(key, value);
    // }
    // }

    public CommonResponse updateAgent(
            String userId,
            String techCodes) {
        CommonResponse result = new CommonResponse();
        if (!StringUtils.hasLength(userId)) {
            result.setCode(-1);
            result.setNote("userId必传");
            return result;
        }
        String[] userIdList = userId.split(";");
        UpdateWrapper<Agent> wrapper = new UpdateWrapper<>();
        wrapper.set("techs", techCodes);
        wrapper.in("agent_id", Arrays.asList(userIdList));
        int rows = agentMapper.update(null, wrapper);
        result.setCode(rows > 0 ? 1 : -1);
        result.setNote(rows > 0 ? "修改成功" : "修改失败");
        return result;
    }

    public Agent queryAgentByAgentId(String agentId) {
        QueryWrapper<Agent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("agent_id", agentId);
        List<Agent> agentList = list(queryWrapper);
        if (!agentList.isEmpty()) {
            return agentList.get(0);
        }
        return null;
    }
}
