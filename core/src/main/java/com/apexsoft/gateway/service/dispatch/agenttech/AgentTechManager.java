package com.apexsoft.gateway.service.dispatch.agenttech;

import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.service.dispatch.AbstractManager;
import com.apexsoft.gateway.service.dispatch.agenttech.dao.AgentTechMapper;
import com.apexsoft.gateway.service.dispatch.agenttech.om.AgentTech;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AgentTechManager extends AbstractManager<String, AgentTech> {

    @Autowired
    private AgentTechMapper agentTechMapper;

    @Override
    protected String name() {
        return "agentTech";
    }

    @Override
    protected List<AgentTech> getAll() {
        return agentTechMapper.getAll();
    }

    @Override
    protected String getKey(AgentTech agentTech) {
        return agentTech.getTechCode();
    }

    @Override
    protected CommonResponse insertOne(AgentTech agentTech) {
        if (agentTech == null) {
            return new CommonResponse(-3, "入参为空");
        }
        if (!StringUtils.hasLength(agentTech.getTechCode())) {
            return new CommonResponse(-4, "技能组编号为空");
        }
        Map<String, Object> columnMap = new HashMap<>();
        columnMap.put("tech_code", agentTech.getTechCode());
        List<AgentTech> existTechs = agentTechMapper.selectByMap(columnMap);
        if (!existTechs.isEmpty()) {
            return new CommonResponse(-1, "该技能组编号已存在[" + agentTech.getTechCode() + "]");
        }
        int count = agentTechMapper.insert(agentTech);
        return new CommonResponse(count > 0 ? count : -2, count > 0 ? "成功" : "失败");
    }

    @Override
    protected CommonResponse delete(String... keys) {
        List<Long> ids = allElements.values()
                .stream()
                .filter(e ->
                        Arrays
                                .stream(keys).anyMatch(k -> k.equals(e.getTechCode()))
                )
                .map(AgentTech::getId)
                .collect(Collectors.toList());
        int count = agentTechMapper.deleteBatchIds(ids);
        return new CommonResponse(count > 0 ? count : -2, count > 0 ? "成功" : "失败");

    }

    @Override
    protected CommonResponse updateOne(AgentTech agentTech) {
        // 创建 UpdateWrapper，设置更新条件
        UpdateWrapper<AgentTech> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("tech_code", agentTech.getTechCode());
        int count = agentTechMapper.update(agentTech, updateWrapper);
        return new CommonResponse(count > 0 ? count : -2, count > 0 ? "成功" : "失败");
    }
}
