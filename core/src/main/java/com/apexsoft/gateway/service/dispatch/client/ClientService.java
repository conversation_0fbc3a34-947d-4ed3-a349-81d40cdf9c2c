package com.apexsoft.gateway.service.dispatch.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.dispatcher.dispatch.Queue;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.CommonService;
import com.apexsoft.gateway.service.dispatch.WitnessUtilService;
import com.apexsoft.gateway.service.dispatch.agent.AgentService;
import com.apexsoft.gateway.service.dispatch.agent.om.Agent;
import com.apexsoft.gateway.service.dispatch.client.dao.WitnessRequestMapper;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import com.apexsoft.util.ConcurrentDateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.jsonwebtoken.lang.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ClientService extends ServiceImpl<WitnessRequestMapper, WitnessRequest>
        implements IService<WitnessRequest> {

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private Queue queue;

    @Autowired
    private WitnessUtilService witnessUtilService;

    @Autowired
    private WitnessRequestMapper witnessRequestMapper;

    @Autowired
    private AgentService agentService;

    @Autowired
    private CommonService commonService;

    // 见证请求尚未结束
    private CommonResponse witnessNotFinish(CommonResponse result, WitnessRequest witnessRequest) {
        JSONObject requestData = (JSONObject) JSON.toJSON(witnessRequest);
        result.setCode(2);
        result.setNote("当前客户还在见证过程中");
        result.setData(requestData);
        return result;
    }

    /**
     * 新增见证请求 @todo 见证请求中的业务数据，要另外创建一张表落地，还是直接在请求表中增加相关业务字段？
     * 
     * @param clientId
     * @return
     */
    public CommonResponse addWitnessRequest(
            String clientId,
            // int org,
            int bizRequestId,

            String test,
            String orgStr,
            String clientName,
            int bizType,
            String bizCode,
            int cusLevel) {
        CommonResponse result = new CommonResponse(1, "成功");
        // @todo 判断客户请求的有效性（类似会话），防止不合法的客户也发起请求。此处可做成模块----使用jwt校验会话
        if (!StringUtils.hasLength(clientId)) {
            result.setCode(-1);
            result.setNote("clientId为空");
            return result;
        }

        // 先判断该笔请求是否已经在队列中
        try {
            // @todo 此处判断是否在队列中，是查数据库好，还是查内存里的数据好？
            WitnessRequest witnessRequest = witnessRequestMapper.queryFirstClient(clientId);
            if (witnessRequest != null) {
                // 表中已有记录
                // state为0，或state为1且result为空，表示此时见证请求还未结束
                int state = witnessRequest.getState();
                switch (state) {
                    case 1:
                        // 撮合成功，判断result是否有值，有值表示见证结束
                        if (StringUtils.hasLength(witnessRequest.getResult())) {
                            break;
                        }
                        return witnessNotFinish(result, witnessRequest);
                    case 0:
                        // 等待中，判断队列中是否还有该请求
                        if (!checkQueueRequest(witnessRequest)) {
                            break;
                        }
                        return witnessNotFinish(result, witnessRequest);
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            log.error("判断该笔请求报错", e);
            result.setCode(-1);
            result.setNote("系统错误");
            return result;
        }
        try {
            // 插入表
            String joinTime = ConcurrentDateUtil.now();
            // @todo bizCode 业务代码，暂时先传空，这里还要获取到客户的一些业务数据
            JSONObject reqData = new JSONObject();
            int score = witnessUtilService.calculateScore(bizCode, cusLevel);
            reqData.put("score", score);

            int org;
            JSONObject data;
            if ((active.equals("dev") || active.equals("devtest")) && StringUtils.hasLength(test)) {
                // 测试数据，用于压力测试时传输相关业务数据
                try {
                    org = Integer.valueOf(orgStr);
                } catch (Exception e) {
                    org = 1;
                }
                data = new JSONObject();
            } else {
                JSONObject paramYwqq = new JSONObject();
                paramYwqq.put("ywqqid", bizRequestId);
                JSONObject ywqqResult = commonService.queryYwqq(paramYwqq);
                if (ywqqResult.getIntValue("code") < 0) {
                    result.setCode(-1);
                    result.setNote("查询业务信息报错：" + ywqqResult.getString("note"));
                    return result;
                }
                if (ywqqResult.get("isSecondary") != null) {
                    data = ywqqResult.getJSONObject("ywqqJson");
                } else {
                    String ywqqStr = ywqqResult.getString("req_data");
                    JSONObject ywqqJson = JSONObject.parseObject(ywqqStr);
                    data = ywqqJson;
                }

                clientName = data.getString("khjc");
                if (clientName == null) {
                    clientName = data.getString("khmc");
                }
                if (clientName == null) {
                    clientName = data.getString("khxm");
                }
                org = data.getIntValue("yyb");
                if (data.getString("ywdm") != null) {
                    bizCode = data.getString("ywdm");
                } else {
                    result.setCode(-1);
                    result.setNote("该见证请求未查到业务代码[ywqqid:" + bizRequestId + "]");
                    return result;
                }
            }

            if (witnessRequestMapper.newWitness(clientId, joinTime, org, bizType, bizCode, cusLevel, bizRequestId,
                    clientName, data.toJSONString()) <= 0) {
                result.setCode(-1);
                result.setNote("新增请求失败");
                return result;
            }
            WitnessRequest witnessRequest = witnessRequestMapper.queryFirstClient(clientId);
            if (witnessRequest == null) {
                result.setCode(-2);
                result.setNote("新增请求失败");
                return result;
            }
            // 插入成功后，插入队列中。如果插入队列失败，要更新表数据
            queue.addRequest(new QueueWitnessRequest(witnessRequest.getId(), witnessRequest));
            JSONObject requestData = (JSONObject) JSON.toJSON(witnessRequest);

            result.setData(requestData);
        } catch (Exception e) {
            // @todo 异常捕获后面要优化
            // e.printStackTrace();
            log.error("新增请求报错", e);
            result.setCode(-999);
            result.setNote("服务器报错");
            return result;
        }

        return result;
    }

    public CommonResponse getWitness(Long id) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try {
            WitnessRequest witnessRequest = witnessRequestMapper.selectById(id);
            JSONObject data = new JSONObject();
            data.put("witnessRequest", witnessRequest);
            response.setData(data);
        } catch (Exception e) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote(e.toString());
        }
        return response;
    }

    public CommonResponse getWitnessCount(String serveAgentId) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try {
            int witnessRequest = witnessRequestMapper.selectCount(serveAgentId);
            JSONObject data = new JSONObject();
            data.put("witnessRequestCount", witnessRequest);
            response.setData(data);
        } catch (Exception e) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote(e.toString());
        }
        return response;
    }

    public CommonResponse modifyWitness(JSONObject params) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "修改成功");
        try {
            WitnessRequest witnessRequest = new WitnessRequest();
            if (params.getString("markerData") != null
                    && !org.apache.commons.lang3.StringUtils.isBlank(params.getString("markerData") + "")) {
                witnessRequest.setMarkerData(params.getString("markerData"));
            }
            if (params.getInteger("id") != null
                    && !org.apache.commons.lang3.StringUtils.isBlank(params.getInteger("id") + "")) {
                witnessRequest.setId(params.getLong("id"));
            }
            int configuration = witnessRequestMapper.modifyWitness(witnessRequest);
            if (configuration >= 0) {
                response.setCode(JSONResponse.CODE_SUCCESS);
            } else {
                response.setCode(JSONResponse.CODE_FAIL);
            }
        } catch (Exception exception) {
            log.error("修改见证锚点失败", exception.getMessage());
            response.setCode(-1);
            response.setNote(exception.getMessage());
            return response;
        }
        return response;
    }

    /**
     * 获取见证请求的详情
     * 
     * @param requestIdStr
     * @return
     */
    public CommonResponse getWitnessRequest(String requestIdStr) {
        CommonResponse result = new CommonResponse(1, "查询成功");
        long requestId = -1;
        try {
            requestId = Long.parseLong(requestIdStr);
        } catch (Exception ignored) {
        }
        try {
            // @todo 此处判断是否在队列中，是查数据库好，还是查内存里的数据好？
            WitnessRequest witnessRequest = witnessRequestMapper.selectById(requestId);
            if (witnessRequest == null) {
                result.setCode(-2);
                result.setNote("无该请求数据");
                return result;
            }
            // 表中已有记录
            if (witnessRequest.getState() == 0) {
                // 等待中，判断队列中是否还有该请求
                checkQueueRequest(witnessRequest);
            }
            JSONObject requestData = (JSONObject) JSON.toJSON(witnessRequest);
            // 见证结束后，查询坐席人员名称
            if (StringUtils.hasLength(witnessRequest.getResult())
                    && StringUtils.hasLength(witnessRequest.getServeAgentId())) {
                Agent agent = agentService.queryAgentByAgentId(witnessRequest.getServeAgentId());
                if (agent != null) {
                    requestData.put("serveAgentName", agent.getAgentName());
                }
            }
            List<QueueWitnessRequest> queueWitnessRequests = queue.getSortedRequests();
            int index = 0;
            for (QueueWitnessRequest queueWitnessRequest : queueWitnessRequests) {
                if (queueWitnessRequest.getRequestId() == requestId) {
                    requestData.put("index", index);
                    break;
                }
                index++;
            }
            result.setData(requestData);
            return result;
        } catch (Exception e) {
            log.error("获取见证请求失败", e);
            result.setCode(-1);
            result.setNote(e.getMessage());
            return result;
        }
    }

    private boolean checkQueueRequest(WitnessRequest witnessRequest) {
        // 等待中，判断队列中是否还有该请求
        if (queue.getRequestById(witnessRequest.getId()) == null) {
            // 队列中不存在客户，表示该客户已离开队列，见证结束
            witnessRequestMapper.witnessFailed(witnessRequest.getId(), -3);
            return false;
        }
        return true;
    }

    /**
     * 取消见证请求
     * 
     * @param requestIdStr
     * @return
     */
    public JSONResponse cancelWitnessRequest(String requestIdStr) {
        JSONResponse result = new JSONResponse(1, "取消成功");
        long requestId = -1;
        try {
            requestId = Long.parseLong(requestIdStr);
        } catch (Exception e) {
            requestId = -1;
        }
        try {
            // if (requestMapper.cancel(requestId) > 0) {
            // // 取消成功
            // }
            // requestSessions.remove(requestId);
            witnessRequestMapper.witnessFailed(requestId, -1);

            // 如果存在呼叫中的坐席，要发送客户已退出排队的消息
            QueueWitnessRequest witnessRequest = queue.getRequestById(requestId);
            if (witnessRequest != null) {
                // witnessRequest.setState(-1);
                // this.witnessFailed(witnessRequest);
                String callAgentId = witnessRequest.getCallingAgentId();
                if (!"".equals(callAgentId)) {
                    // agentSessionService.endCall(callAgentId);
                    agentService.sendCallEnd(callAgentId);
                }
            }

            queue.removeRequest(requestId);
        } catch (Exception e) {
            log.error("取消见证请求失败", e);
            result.setCode(-1);
            result.setNote(e.getMessage());
        }
        return result;
    }

    public void accept(QueueWitnessRequest request) {
        witnessRequestMapper.accept(request.getRequestId(), request.getCallingAgentId(), ConcurrentDateUtil.now());
    }

    public void witnessFailed(WitnessRequest request) {
        witnessRequestMapper.witnessFailed(request.getId(), request.getState());
    }

    public void witnessResult(long requestId, String result, String reason) {
        witnessRequestMapper.witnessResult(requestId, ConcurrentDateUtil.now(), result, reason);
    }

    public void agentOffline(String agentId) {
        witnessRequestMapper.agentOffline(agentId, ConcurrentDateUtil.now(), "false", "坐席掉线");
    }

    // 查询已完成的见证流水（坐席任务查询）
    public JSONResponse queryFinishedWitness(
            String clientId,
            String clientName,
            String org,
            String agentId,
            String agentName,
            String serveTimeBegin,
            String serveTimeEnd,
            int pageNumber, int pageSize) {
        QueryWrapper<WitnessRequest> queryWrapper = new QueryWrapper<>();
        // 只查询见证流程结束的记录
        queryWrapper.eq("state", 1);
        queryWrapper.isNotNull("result")
                .ne("result", "");

        witnessUtilService.likeWrapper(queryWrapper, "client_id", clientId);
        witnessUtilService.likeWrapper(queryWrapper, "client_name", clientName);
        if (StringUtils.hasLength(org)) {
            String[] orgs = org.split(";");
            queryWrapper.in("org", Arrays.asList(orgs));
        }
        witnessUtilService.likeWrapper(queryWrapper, "serve_agent_id", agentId);
        if (StringUtils.hasLength(agentName)) {
            CommonResponse agentListResult = agentService.queryAgent(
                    null,
                    agentName,
                    null,
                    null,
                    null,
                    -1,
                    -1);
            if (agentListResult.getCode() > 0) {
                JSONArray agentList = agentListResult.getRecords();
                List<String> agentIds = agentList.stream().map(agent -> {
                    JSONObject agentJson = (JSONObject) agent;
                    return agentJson.getString("agentId");
                }).collect(Collectors.toList());
                queryWrapper.in("serve_agent_id", agentIds);
            }
        }
        if (StringUtils.hasLength(serveTimeBegin)) {
            queryWrapper.gt("serve_time", serveTimeBegin);
        }
        if (StringUtils.hasLength(serveTimeEnd)) {
            queryWrapper.lt("serve_time", serveTimeEnd);
        }
        // 按id倒序
        queryWrapper.orderByDesc("id");
        CommonResponse result = new CommonResponse();
        // 创建分页对象，设置当前页和每页显示的数量
        Page<WitnessRequest> page = new Page<>(pageNumber, pageSize);

        Page<WitnessRequest> pageResult = page(page, queryWrapper);
        result.setData(JSON.parseObject(JSON.toJSONString(pageResult)));
        return result;
    }

    // 查询坐席当天服务的客户列表
    public JSONResponse queryTodayServiceList(String serveAgentId) {
        List<WitnessRequest> witnessRequests = witnessRequestMapper.queryTodayServiceList(serveAgentId);
        CommonResponse result = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");

        // 创建新的数组来存储过滤后的数据
        JSONArray filteredList = new JSONArray();

        for (WitnessRequest witnessRequest : witnessRequests) {
            // 解析 reqData 获取身份证号
            JSONObject reqDataObj = JSON.parseObject(witnessRequest.getReqData());
            String sfzh = reqDataObj.getString("sfzh");

            // 创建只包含需要字段的新对象
            JSONObject item = new JSONObject();
            item.put("clientName", witnessRequest.getClientName());
            item.put("serveTime", witnessRequest.getServeTime());
            item.put("sfzh", sfzh); // 添加从 reqData 中提取的身份证号

            // 将新对象添加到结果列表
            filteredList.add(item);
        }

        JSONObject data = new JSONObject();
        data.put("list", filteredList); // 使用过滤后的列表
        result.setData(data);
        return result;
    }

}
