package com.apexsoft.gateway.service.dispatch.bizcode;

import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.service.dispatch.AbstractManager;
import com.apexsoft.gateway.service.dispatch.bizcode.dao.BizCodeMapper;
import com.apexsoft.gateway.service.dispatch.bizcode.om.BizCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

// @todo AbstractManager的抽象方法还没完全实现
@Service
@Slf4j
public class BizCodeManager extends AbstractManager<String, BizCode> {

    @Autowired
    private BizCodeMapper bizCodeMapper;

    @Override
    protected String name() {
        return "bizCode";
    }

    @Override
    protected List<BizCode> getAll() {
        return bizCodeMapper.getAll();
    }

    @Override
    protected String getKey(BizCode bizCode) {
        return bizCode.getBizCode();
    }

    @Override
    protected CommonResponse insertOne(BizCode bizCode) {
        return null;
    }

    @Override
    protected CommonResponse delete(String... keys) {
        return null;
    }

    @Override
    protected CommonResponse updateOne(BizCode bizCode) {
        return null;
    }
}
