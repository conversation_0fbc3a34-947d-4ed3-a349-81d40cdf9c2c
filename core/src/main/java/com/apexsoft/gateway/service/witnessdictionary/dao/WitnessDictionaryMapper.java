package com.apexsoft.gateway.service.witnessdictionary.dao;

import com.apexsoft.gateway.service.witnessdictionary.om.WitnessDictionary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface WitnessDictionaryMapper extends BaseMapper<WitnessDictionary> {

    // @Select("SELECT * FROM witness_dictionary WHERE dic_code = #{dicCode}")
    // List<WitnessDictionary> selectByCode(@Param("dicCode") String dicCode);

    // @Select("<script>SELECT * FROM witness_dictionary WHERE dic_code IN " +
    // "<foreach item='item' index='index' collection='dicCodes' open='('
    // separator=',' close=')'>#{item}</foreach>"
    // +
    // "</script>")
    // List<WitnessDictionary> selectByCodes(@Param("dicCodes") String[] dicCodes);
}
