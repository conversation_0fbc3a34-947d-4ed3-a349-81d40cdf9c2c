package com.apexsoft.gateway.service.dispatch.agent.dao;

import com.apexsoft.gateway.service.dispatch.agent.om.Agent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface AgentMapper extends BaseMapper<Agent> {

    @Select("select * from agent where agent_id = #{agentId}")
    Agent selectByAgentId (String agentId);
}
