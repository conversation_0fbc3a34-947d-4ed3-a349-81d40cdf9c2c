package com.apexsoft.gateway.service.opratelog.om;

import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@RequiredArgsConstructor
public class ManagerOperateLog implements Serializable {
    @NonNull
    private String agentId;
    @NonNull
    private String menu;
    private LocalDateTime operateTime;
    @NonNull
    private Integer action;
    @NonNull
    private String data;
    @NonNull
    private String responseData;
}
