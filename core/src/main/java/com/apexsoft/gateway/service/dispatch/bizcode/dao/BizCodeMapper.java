package com.apexsoft.gateway.service.dispatch.bizcode.dao;

import com.apexsoft.gateway.service.dispatch.bizcode.om.BizCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface BizCodeMapper extends BaseMapper<BizCode> {

    @Select("SELECT biz_code, biz_name, biz_level FROM business_code")
    List<BizCode> getAll ();
}
