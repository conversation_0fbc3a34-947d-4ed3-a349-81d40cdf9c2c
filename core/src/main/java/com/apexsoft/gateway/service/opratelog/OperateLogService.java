package com.apexsoft.gateway.service.opratelog;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.opratelog.dao.ManagerOperateLogMapper;
import com.apexsoft.gateway.service.opratelog.om.ManagerOperateLog;
import com.apexsoft.live.session.UserAuthenticateSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class OperateLogService {

    @Autowired
    private ManagerOperateLogMapper operateLogMapper;

    @Autowired
    private HttpServletRequest httpServletRequest;

    public void addNewLog (String menu, Integer action, JSONObject data, JSONResponse responseData) {
        this.addNewLog(menu, action, data.toJSONString(), responseData);
    }

    /**
     * 新增日志
     *
     * @param menu
     * @param action
     * @param data
     * @param responseData
     */
    public void addNewLog (String menu, Integer action, String data, JSONResponse responseData) {
        // 从 HttpServletRequest 中获取信息
        final AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        CompletableFuture.runAsync(() -> {
            String id = user.getUserId();
            ManagerOperateLog managerOperateLog = new ManagerOperateLog(
                    id,
                    menu,
                    action,
                    data,
                    JSONObject.toJSONString(responseData)
            );
            try {
                operateLogMapper.insert(managerOperateLog);
            } catch (Exception e) {
                log.error("记录操作日志失败[{}]", JSONObject.toJSONString(managerOperateLog), e);
            }
        });
    }
}
