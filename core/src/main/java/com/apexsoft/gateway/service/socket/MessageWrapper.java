package com.apexsoft.gateway.service.socket;

public class MessageWrapper {
    private String type;
    private Object data;
    private String contentType; // 内容类型，例如 "application/json", "image/jpeg"
    private String purpose;// 拍照进行人脸比对的时候会传这个字段

    public MessageWrapper() {
    }

    public MessageWrapper(String type, Object data) {
        this.type = type;
        this.data = data;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }
}
