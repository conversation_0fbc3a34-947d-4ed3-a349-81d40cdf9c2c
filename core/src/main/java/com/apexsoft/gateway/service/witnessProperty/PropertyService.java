package com.apexsoft.gateway.service.witnessProperty;

import org.springframework.stereotype.Service;

import com.apexsoft.gateway.service.witnessProperty.dao.WitnessPropertyMapper;
import com.apexsoft.gateway.service.witnessProperty.om.WitnessProperty;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
@Slf4j
public class PropertyService extends ServiceImpl<WitnessPropertyMapper, WitnessProperty> {

  /**
   * 通过参数代码查询系统参数
   *
   * @param code 参数代码
   * @return 系统参数
   */
  public WitnessProperty getByCode(String code) {
    if (!StringUtils.hasText(code)) {
      return null;
    }
    LambdaQueryWrapper<WitnessProperty> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(WitnessProperty::getCode, code);
    return getOne(queryWrapper);
  }

  /**
   * 通过参数名称查询系统参数
   *
   * @param name 参数名称
   * @return 系统参数
   */
  public WitnessProperty getByName(String name) {
    if (!StringUtils.hasText(name)) {
      return null;
    }
    LambdaQueryWrapper<WitnessProperty> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(WitnessProperty::getName, name);
    return getOne(queryWrapper);
  }

  /**
   * 条件查询系统参数列表
   *
   * @param property 查询条件
   * @return 系统参数列表
   */
  public List<WitnessProperty> listProperties(WitnessProperty property) {
    QueryWrapper<WitnessProperty> queryWrapper = new QueryWrapper<>();
    if (property != null) {
      if (StringUtils.hasText(property.getCode())) {
        queryWrapper.like("code", property.getCode());
      }
      if (StringUtils.hasText(property.getName())) {
        queryWrapper.like("name", property.getName());
      }
      if (StringUtils.hasText(property.getValue())) {
        queryWrapper.like("value", property.getValue());
      }
    }
    return list(queryWrapper);
  }

  /**
   * 添加系统参数
   *
   * @param property 系统参数
   * @return 是否添加成功
   */
  public boolean addProperty(WitnessProperty property) {
    if (property == null || !StringUtils.hasText(property.getCode()) || !StringUtils.hasText(property.getName())) {
      log.error("添加系统参数失败：参数代码或名称为空");
      return false;
    }

    // 检查code和name是否已存在
    if (getByCode(property.getCode()) != null) {
      log.error("添加系统参数失败：参数代码已存在");
      return false;
    }
    if (getByName(property.getName()) != null) {
      log.error("添加系统参数失败：参数名称已存在");
      return false;
    }

    return save(property);
  }

  /**
   * 更新系统参数
   *
   * @param property 系统参数
   * @return 是否更新成功
   */
  public boolean updateProperty(WitnessProperty property) {
    if (property == null || !StringUtils.hasText(property.getCode())) {
      log.error("更新系统参数失败：参数代码为空");
      return false;
    }

    WitnessProperty existProperty = getByCode(property.getCode());
    if (existProperty == null) {
      log.error("更新系统参数失败：参数不存在");
      return false;
    }

    // 如果修改了name，需要检查新name是否已存在
    if (StringUtils.hasText(property.getName()) && !property.getName().equals(existProperty.getName())) {
      WitnessProperty nameExist = getByName(property.getName());
      if (nameExist != null) {
        log.error("更新系统参数失败：新参数名称已存在");
        return false;
      }
    }

    LambdaQueryWrapper<WitnessProperty> updateWrapper = new LambdaQueryWrapper<>();
    updateWrapper.eq(WitnessProperty::getCode, property.getCode());
    return update(property, updateWrapper);
  }

  /**
   * 删除系统参数
   *
   * @param code 参数代码
   * @return 是否删除成功
   */
  // public boolean deleteByCode(String code) {
  // if (!StringUtils.hasText(code)) {
  // log.error("删除系统参数失败：参数代码为空");
  // return false;
  // }

  // LambdaQueryWrapper<WitnessProperty> queryWrapper = new
  // LambdaQueryWrapper<>();
  // queryWrapper.eq(WitnessProperty::getCode, code);
  // return remove(queryWrapper);
  // }

  /**
   * 批量删除系统参数
   *
   * @param codes 参数代码数组
   * @return 是否全部删除成功
   */
  public boolean deleteByCodeBatch(String[] codes) {
    if (codes == null || codes.length == 0) {
      log.error("批量删除系统参数失败：参数代码数组为空");
      return false;
    }

    LambdaQueryWrapper<WitnessProperty> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.in(WitnessProperty::getCode, (Object[]) codes);
    return remove(queryWrapper);
  }
}
