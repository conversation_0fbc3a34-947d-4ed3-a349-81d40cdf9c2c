package com.apexsoft.gateway.service.dispatch.bizlevel.dao;

import com.apexsoft.gateway.service.dispatch.bizlevel.om.BizLevel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface BizLevelMapper extends BaseMapper<BizLevel> {

    @Select("SELECT biz_level, score FROM business_level_config")
    List<BizLevel> getAll ();
}
