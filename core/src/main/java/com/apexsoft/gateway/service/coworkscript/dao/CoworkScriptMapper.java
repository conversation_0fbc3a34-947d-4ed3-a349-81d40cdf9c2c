package com.apexsoft.gateway.service.coworkscript.dao;

import com.apexsoft.gateway.service.coworkscript.om.CoworkScript;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 揭示信息数据访问层
 * 继承 BaseMapper 即可获得基本的 CRUD 操作
 */
@Mapper
public interface CoworkScriptMapper extends BaseMapper<CoworkScript> {
  // MyBatis Plus 提供的基本方法已经足够使用：
  // - insert(entity): 插入一条记录
  // - deleteById(id): 根据ID删除
  // - updateById(entity): 根据ID更新
  // - selectById(id): 根据ID查询
  // - selectList(wrapper): 条件查询
  // - selectPage(page, wrapper): 分页查询
}