package com.apexsoft.gateway.service.dispatch;

import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.service.dispatch.bizcode.BizCodeManager;
import com.apexsoft.gateway.service.dispatch.bizcode.om.BizCode;
import com.apexsoft.gateway.service.dispatch.bizlevel.BizLevelManager;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import com.apexsoft.gateway.service.dispatch.cuslevel.CusLevelManager;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class WitnessUtilService implements Constant {

    @Autowired
    private BizCodeManager bizCodeManager;

    @Autowired
    private BizLevelManager bizLevelManager;

    @Autowired
    private CusLevelManager cusLevelManager;

    public String getBizLevel(String bizCodeStr) {
        BizCode bizCode = bizCodeManager.get(bizCodeStr, null);
        String bizLevel = YWDJ_DEFAULT;
        // bizLevel可能是0，可能是空，这种情况下都取默认值
        if (bizCode != null && !"".equals(bizCode.getBizLevel()) && !"0".equals(bizCode.getBizLevel())) {
            bizLevel = bizCode.getBizLevel();
        }
        return bizLevel;
    }

    public int getCusLevel(WitnessRequest witnessRequest) {
        int cusLevel = witnessRequest.getCusLevel();
        if (cusLevel <= 0) {
            return KHDJ_DEFAULT;
        }
        return cusLevel;
    }

    public int calculateScore (String bizCodeStr, int cusLevel) {
        String bizLevelStr = getBizLevel(bizCodeStr);
//        int cusLevel = getCusLevel(witnessRequest);
        cusLevel = cusLevel <= 0 ? KHDJ_DEFAULT : cusLevel;
        int bizLevel;
        try { bizLevel = Integer.parseInt(bizLevelStr); } catch (Exception e) { bizLevel = Integer.parseInt(YWDJ_DEFAULT); }
        int bizLevelScore = bizLevelManager.getBizLevelScore(bizLevel);
        int cusLevelScore = cusLevelManager.getCusLevelScore(cusLevel);

        return bizLevelScore + cusLevelScore;
    }

    public <T> void likeWrapper (QueryWrapper<T> queryWrapper, String key, String value) {
        if (StringUtils.hasLength(value)) {
            // LIKE 条件
            queryWrapper.like(key, value);
        }
    }
}
