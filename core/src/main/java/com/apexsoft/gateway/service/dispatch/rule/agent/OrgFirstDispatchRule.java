package com.apexsoft.gateway.service.dispatch.rule.agent;

import com.apexsoft.dispatcher.dispatch.rule.AgentDispatchRule;
import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.service.dispatch.agent.om.Agent;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 营业部优先规则
 */
@Component("2001")
@Slf4j
public class OrgFirstDispatchRule extends AgentDispatchRule<Agent> {

    private final static int HEAD_ORG = 1;

    @Override
    public boolean canServe(QueueWitnessRequest queueWitnessRequest, AgentSession<Agent> agentSession, Date nowDate) {
        // 营业部优先的规则
        if (agentSession.getAgentInfo().getOrg() == HEAD_ORG) {
            // 当前类型为总部或者营业部总部都可以的状态
            log.info("head org agent[{}] can serve request[{}]", agentSession.getAgentId(), queueWitnessRequest.getRequestId());
            return true;
        }
        WitnessRequest witnessRequest = queueWitnessRequest.getWitnessRequest();
        if (witnessRequest.getOrg() == agentSession.getAgentInfo().getOrg()) {
            boolean isOrgFirst = ruleValueMatch(witnessRequest.getOrg()) > -1;
            if (isOrgFirst) {
                // 营业部优先的情况下，不管客户此时在往总部发还是营业部发，都可以
                log.info("agent[{}] can serve request[{}]", agentSession.getAgentId(), queueWitnessRequest.getRequestId());
                return true;
                // 非营业部优先的，只能往总部发
            }
        }
        // 坐席营业部和客户营业部未匹配，或者该营业部不是营业部优先且坐席是营业部坐席的，都不能为该客户服务
        log.warn("check agent[{}] can not serve request[{}]", agentSession.getAgentId(), queueWitnessRequest.getRequestId());
        return false;
    }

    @Override
    public int sortCompare(
            AgentSession<Agent> agentSession1,
            AgentSession<Agent> agentSession2,
            QueueWitnessRequest queueWitnessRequest
    ) {
        // @todo 需要检查下这里返回的FISRT_ELE_TO_BEHIND值有没有问题
        if (agentSession1.getAgentInfo().getOrg() == HEAD_ORG && agentSession2.getAgentInfo().getOrg() != HEAD_ORG) {
            return FISRT_ELE_TO_BEHIND;
        }
        if (agentSession1.getAgentInfo().getOrg() != HEAD_ORG && agentSession2.getAgentInfo().getOrg() == HEAD_ORG) {
            return -FISRT_ELE_TO_BEHIND;
        }
        return 0;
    }
}
