package com.apexsoft.gateway.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.client.Request;
import com.apexsoft.gateway.aas.common.session.UserSession;
import com.apexsoft.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.gateway.base.intercept.HttpContext;
import com.apexsoft.gateway.common.redis.LockManager;
import com.apexsoft.gateway.common.redis.RedisStringDao;
import com.apexsoft.gateway.common.utils.AddressUtils;
import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.common.utils.FileUtil;
import com.apexsoft.gateway.common.utils.HttpsSecureProtocolSocketFactory;
import com.apexsoft.gateway.dao.CommonEsbDao;
import com.github.jaiimageio.impl.plugins.tiff.TIFFImageReaderSpi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.multipart.FilePart;
import org.apache.commons.httpclient.methods.multipart.MultipartRequestEntity;
import org.apache.commons.httpclient.methods.multipart.Part;
import org.apache.commons.httpclient.protocol.Protocol;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.spi.IIORegistry;
import javax.imageio.spi.ImageReaderSpi;
import javax.imageio.stream.FileImageInputStream;
import javax.imageio.stream.ImageInputStream;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.List;


@Slf4j
@Component
public class CommonService{



    @Resource
    LockManager lockManager;

    @Autowired
    RedisStringDao redisStringDao;

    @Autowired
    CommonEsbDao commonEsbDao;

    @Value("${application.imageDic}")
    private String imagePath;

    @Value("${application.YGT3_URL}")
    private String YGT3_URL;

    private static String IMAGE_LOCK_KEY_PRE = "VUE.IMAGE_LOCK_";


    public JSONObject queryXtcs(String csdm){
        JSONObject param=new JSONObject();
        param.put("csdm",csdm);
        return commonEsbDao.queryXtcs(param);
    }


    public JSONObject queryYwqq(JSONObject paramYwqq){
        JSONObject cxywqqRes = commonEsbDao.queryYwqq(paramYwqq);
        if(cxywqqRes.getInteger("code") > 0 && cxywqqRes.getString("req_data") != ""){
            String ywqqStr = cxywqqRes.getString("req_data");
            JSONObject ywqqJson = JSONObject.parseObject(ywqqStr);
            String khh = ywqqJson.getString("khh");
            if(!"KHSQ88888888".equals(khh)){
                JSONObject paramKhxx =new JSONObject();
                paramKhxx.put("srbs",khh);
                paramKhxx.put("bslx","0");
                JSONObject queryKhxxRes = commonEsbDao.queryKhxx(paramKhxx);
                queryKhxxRes.put("ywqqJson",ywqqJson);
                queryKhxxRes.put("isSecondary",true);
                return queryKhxxRes;
            }
        }
        return cxywqqRes;
    }

    public JSONObject cxgyxx(String userId){
        JSONObject p =  new JSONObject();
        p.put("userid",userId);
        JSONObject cxywqqRes = commonEsbDao.cxgyxx(p);
        return cxywqqRes;
    }

    public JSONObject queryYwqqSj(JSONObject paramYwqq){
        JSONObject cxywqqSjRes = commonEsbDao.queryYwqqSj(paramYwqq);
        return cxywqqSjRes;
    }

    public JSONObject execGawsqForPhoto(JSONObject p){
        JSONObject result = commonEsbDao.execGawsqForPhoto(p);
        return result;
    }

    public JSONObject queryGawsqFkjg(JSONObject p){
        JSONObject result = commonEsbDao.queryGawsqFkjg(p);
        return result;
    }

    public JSONObject cxCoworkInfo(JSONObject p){
        JSONObject result = commonEsbDao.cxCoworkInfo(p);
        return result;
    }

    public JSONObject queryDictionary(JSONObject p){
        JSONObject res = new JSONObject();
        String fldm = p.getString("fldm");
        String[] fldms = fldm.split(";");
        for(int i = 0;i < fldms.length; i++){
            JSONObject params = new JSONObject();
            params.put("fldm",fldms[i]);
            JSONObject result = commonEsbDao.queryDictionary(params);
            if(result.getInteger("code") > 0 && result.getJSONArray("records").size() > 0){
                res.put(fldms[i],result.getJSONArray("records"));
            }
        }
        return res;
    }

    public JSONObject queryOrg(JSONObject p){
        String gyid = p.getString("gyid");
        JSONObject params = new JSONObject();
        params.put("gyid",gyid);
        JSONObject result = commonEsbDao.queryOrg(params);
        return result;
    }

    public JSONObject getYxrwmxByYwqqid(JSONObject p){
        JSONObject result = commonEsbDao.getYxrwmxByYwqqid(p);
        return result;
    }

    public JSONObject getZsrwjgByYwqqid(JSONObject p){
        JSONObject result = commonEsbDao.getZsrwjgByYwqqid(p);
        return result;
    }

    public JSONObject comparePhoto(JSONObject p){
        JSONObject result = commonEsbDao.comparePhoto(p);
        return result;
    }

    public JSONObject cxywqqyscs(JSONObject p){
        JSONObject result = commonEsbDao.cxywqqyscs(p);
        return result;
    }

    public JSONObject cxkhdrywfqcsjthyy(JSONObject p){
        JSONObject result = commonEsbDao.cxkhdrywfqcsjthyy(p);
        return result;
    }

    public JSONObject cxgyxx(JSONObject p){
        String userid = p.getString("gyid");
        JSONObject params = new JSONObject();
        params.put("userid",userid);
        JSONObject result = commonEsbDao.cxgyxx(params);
        return result;
    }

    /**
     * tiff切割成jpg
     * @param tifPth
     * @param filepath
     * @return
     */
    public JSONArray splitTif(String tifPth,String filepath){
        JSONArray imageList = new JSONArray();
        ImageInputStream input = null;
        try{
            File tiffFile = new File(tifPth);
            input = new FileImageInputStream(tiffFile);
            IIORegistry iioreg = IIORegistry.getDefaultInstance();
            iioreg.registerApplicationClasspathSpis();
            ImageReaderSpi irs = new TIFFImageReaderSpi();
            ImageReader tiffReader = irs.createReaderInstance();
            tiffReader.setInput(input);

            int pages = tiffReader.getNumImages(true);
            File maxPageJpg = new File(Constant.imageDic+File.separator+filepath+"_"+(pages-1));
            if(!maxPageJpg.exists()){
                if(pages>0){
                    for (int i = 0; i < pages; i++) {
                        String fileP = tifPth+"_" + i;
                        BufferedImage bi=tiffReader.read(i);
                        Image image = bi.getScaledInstance(bi.getWidth(), bi.getHeight(), Image.SCALE_DEFAULT);
                        BufferedImage tag = new BufferedImage(bi.getWidth(), bi.getHeight(), BufferedImage.TYPE_INT_BGR);
                        Graphics g = tag.getGraphics();
                        g.drawImage(image, 0, 0, null);
                        g.dispose();
                        ImageIO.write(tag, "jpg", new File(fileP));
                        imageList.add("/image/"+filepath+"_"+i);
                    }
                }
            }else{
                for (int i = 0; i < pages; i++) {
                    String fileP = filepath+"_" + i;
                    imageList.add("/image/"+fileP);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("图片分割报错"+e.getMessage(),e);
        }finally {
            if(input!=null) {
                try {
                    input.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    log.error("图片分割 关闭报错"+e.getMessage());
                }
            }
        }
        return imageList;
    }

    public JSONObject downloadFileToBase64(String filepath){
        Request request = new Request()
                .setService("esb.ygt.filedownload")
                .put("filepath", filepath);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        JSONObject result = commonEsbDao.downfile(request,outputStream);

        if(result.getIntValue("code")>0){
            transferFile(result, outputStream);
        }

        return  result;

    }

    public JSONObject downloadFile(OutputStream outputStream,String filepath){
        Request request = new Request()
                .setService("esb.ygt.filedownload")
                .put("filepath", filepath);
        JSONObject result = commonEsbDao.downfile(request,outputStream);
        return  result;
    }

    private JSONObject transferFile (JSONObject result, ByteArrayOutputStream outputStream) {
        String base64 = FileUtil.outputToBase64(outputStream);
        JSONObject img = new JSONObject();
        img.put("imgBase64",base64);
        result.put("imgBase64",base64);
        return result;
    }

    /**
     * 下载图片缓存文件
     * @param response
     * @param filepath
     * @return
     */
    public String downloadImage( HttpServletResponse response,  String filepath) {
        String imageFile = Constant.imageDic+File.separator+filepath;
        File file =new File(imageFile);
        if(file.exists()) {
            ServletOutputStream out = null;

            try {
                out = response.getOutputStream();
                org.apache.commons.io.FileUtils.copyFile(file,out);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (out != null) {
                    try {
                        out.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            return null;
        }else{
            return "影像不存在";
        }
    }


    /**
     * 获取文件流的文件格式，只返回tif和jpg
     * @param filePath
     * @return
     * @throws IOException
     */
    public static  String getFileType(String filePath) throws IOException {
        FileInputStream is = new FileInputStream(filePath);
        byte[] b = new byte[2];
        is.read(b, 0, b.length);
        String value = FileUtil.bytesToHexString(b).toUpperCase();
        is.close();
        if ("4949".equals(value) || "4D4D".equals(value)) {
            return "tif";
        }else if("FFD8".equals(value)){
            return "jpg";
        }else{
            return "";
        }
    }

    public String setUUID(HttpServletRequest request){
        String uuid= UUID.randomUUID().toString();
        AuthUser<JSONObject> authUser = UserSession.getUserSession();
        JSONObject obj = new JSONObject();
        obj.put("uid", authUser.getId());
        obj.put("opStation", AddressUtils.getIntranetIp(request));
        redisStringDao.setKey(uuid, obj.toString(),5);
        return uuid;
    }

    public String getCookieLang(){
        String langName = "";
        String langCookieName = "lbLang";
        HttpServletRequest request = HttpContext.getRequest();
        Cookie[] cookies = request.getCookies();
        if(cookies!=null){
            for(Cookie cookie: cookies){
                if(cookie.getName().equals(langCookieName)){
                    langName = cookie.getValue();
                }
            }
        }
        return langName;
    }

    public String getDefaultCookieLang(){
        String langName = getCookieLang();
        return langName.isEmpty()? Constant.lang_en:langName;
    }

    public JSONObject modifyReqData(String ywqqid,JSONObject data) {
        JSONObject reqData;
        JSONObject paramYwqq = new JSONObject();
        paramYwqq.put("ywqqid",ywqqid);
        JSONObject obj = commonEsbDao.queryYwqq(paramYwqq);;
        if (obj.getIntValue("code") > 0) {
            reqData = JSONObject.parseObject(obj.getString("req_data"));
            reqData.putAll(data);
            JSONObject result = commonEsbDao.modifyAccept(reqData);
            return  result;
        }
        return obj;
    }

    /**
     * 上传影像
     * @param request
     * @return
     */
    public JSONObject uploadImage(HttpServletRequest request) {
        String basePath = imagePath;
        log.info("---影像上传---basePath"+basePath);
        DiskFileItemFactory factory = new DiskFileItemFactory();
        File  fileM = new File(basePath);
        if(!fileM.exists()){
            fileM.mkdirs();
        }
        File _upLoadFile=null;
        JSONObject responseJson = new JSONObject();
        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            MultiValueMap<String, MultipartFile> multiFileMap = multipartRequest.getMultiFileMap();
            List<MultipartFile> fileSet = new LinkedList<>();
            for(Map.Entry<String, List<MultipartFile>> temp : multiFileMap.entrySet()){
                List<MultipartFile> _fileSet = temp.getValue();
                fileSet.addAll(_fileSet);
            }
            log.info("---影像上传---fileSet的总张数"+fileSet.size());
            List<File> targetFileList = new ArrayList<>();
            if(fileSet == null || fileSet.size()<=0){
                responseJson.put("code", -1);
                responseJson.put("note", "没有文件流");
                return responseJson;
            }else{
                if(fileSet.size()>1){
                    for (int i = 0; i < fileSet.size(); i++) {
                        MultipartFile fileItem = fileSet.get(i);
                        if (fileItem.isEmpty()) {
                            continue;
                        }
                        UUID uuid4 = UUID.randomUUID();
                        File targetFile = new File(basePath + File.separator + uuid4 + (int) (Math.random() * 1000)+".jpg");
                        fileItem.transferTo(targetFile);
                        targetFileList.add(targetFile);
                    }
                    log.info("---影像上传---targetFileList的总张数"+targetFileList.size());
                    UUID uuid4 = UUID.randomUUID();
                    _upLoadFile = new File(basePath + File.separator + uuid4 + (int) (Math.random() * 1000)+".tiff");
                    List<String> list = new ArrayList<>();
                    for (File file : targetFileList) {
                        list.add(file.getAbsolutePath());
                    }
                    log.info("---影像上传---list"+list.size());
//                    TiffUtil.mergeJpg2MutliTiff(_upLoadFile.getAbsolutePath(), list);
                }else{
                    UUID uuid4 = UUID.randomUUID();
                    String path = basePath + File.separator + uuid4 + (int) (Math.random() * 1000)+".jpg";
                    _upLoadFile = new File(path);
                    MultipartFile fileItem = fileSet.get(0);
                    fileItem.transferTo(_upLoadFile);
                }
            }
            if(YGT3_URL.indexOf("https")>=0) {
                Protocol myhttps = new Protocol("https", new HttpsSecureProtocolSocketFactory(), 443);
                Protocol.registerProtocol("https", myhttps);
            }
            PostMethod filePost = new PostMethod(YGT3_URL+"/MediaService.service?action=fileUpload&FILENAME="+_upLoadFile.getName()+"&fqr=0");//mediaDownloadStream
            HttpClient client = null;
            Part[] parts = {
                    new FilePart(_upLoadFile.getName(), _upLoadFile)
            };
            filePost.setRequestEntity(new MultipartRequestEntity(parts, filePost.getParams()));
            client = new HttpClient();
            client.getHttpConnectionManager().getParams().setConnectionTimeout(5000);
            int status = client.executeMethod(filePost);
            if (status == HttpStatus.SC_OK) {
                String responseText = filePost.getResponseBodyAsString();
                log.info("影像上传标志responseText"+responseText);
                try {
                    responseJson = JSONObject.parseObject(responseText);
                }catch (Exception e){
                    log.info("影像上传标志responseJson"+responseJson);
                }
                //成功
                if (responseJson.getIntValue("ret") == 1) {
                    responseJson.put("code", 1);
                    JSONObject data = new JSONObject();
                    data.put("yxlx", request.getParameter("yxlx"));
                    data.put("ywqqid", request.getParameter("ywqqid"));
                    data.put("msg", "上传成功");
                    data.put("filepath",responseJson.getString("FILEPATH"));
                    responseJson.put("data",data);
                    responseJson.remove("FILEPATH");
                    responseJson.remove("msg");

                    log.info(responseJson.toJSONString());
                    return responseJson;
                }else{
                    responseJson.put("code", -1);
                    responseJson.put("note", responseJson.getString("msg"));
                }
            }else{
                responseJson.put("code", -1);
                responseJson.put("note", "上传失败:请求一柜通错误status："+status);
            }
            if(_upLoadFile.exists()) {
                _upLoadFile.delete();
            }
            for (File file : targetFileList) {
                file.delete();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            responseJson.put("code", -1);
            responseJson.put("note", "上传失败" + e.getMessage());
        }
        return responseJson;
    }
}
