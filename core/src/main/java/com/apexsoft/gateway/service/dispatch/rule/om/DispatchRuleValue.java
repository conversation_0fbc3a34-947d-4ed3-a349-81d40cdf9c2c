package com.apexsoft.gateway.service.dispatch.rule.om;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "派单规则值", description = "具体的派单规则值")
@Data
public class DispatchRuleValue implements Serializable {

    @ApiModelProperty(value = "规则代码", name = "ruleCode", required = true, example = "1001")
    private String ruleCode;

    @ApiModelProperty(value = "规则值", name = "ruleValue", example = "3|2|1")
    private String ruleValue;
}
