package com.apexsoft.gateway.service.dispatch.sync;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.dao.UserDao;
import com.apexsoft.gateway.service.dispatch.agent.dao.AgentMapper;
import com.apexsoft.gateway.service.dispatch.agent.om.Agent;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CifSyncService extends ServiceImpl<AgentMapper, Agent> implements IService<Agent>, Constant {

    @Autowired
    private UserDao userDao;

    @Autowired
    private AgentMapper agentMapper;

    // 每天 24:00 执行任务
    @Scheduled(cron = "0 0 0 * * ?")
    @Transactional
    public void syncTask () {
        syncAgent();
    }

    @Transactional
    public void syncAgent () {
        log.info("同步cif用户的视频见证人到见证系统");
        JSONObject result = userDao.queryUserByGysx(WITNESS_GYSX);
        if (result.getIntValue("code") < 0) {
            log.error("同步cif用户失败:{}", result.getString("note"));
            return;
        }

        List<Agent> agentList = agentMapper.selectList(null);
        Map<String, Agent> allAgent = new HashMap<>();
        agentList.forEach(agent -> allAgent.put(agent.getAgentId(), agent));
        JSONArray array = result.getJSONArray("records");
        List<Agent> newAgentList = new ArrayList<>();
        for (int i = 0; i < array.size();i++) {
            JSONObject cifUser = array.getJSONObject(i);
            Agent agent = allAgent.get(cifUser.getString("userid"));
            if (agent == null) {
                agent = new Agent();
            }
            agent.setAgentId(cifUser.getString("userid"));
            agent.setAgentName(cifUser.getString("name"));
            agent.setOrg(cifUser.getIntValue("orgid"));
            newAgentList.add(agent);
        }
        // 全部删除旧的坐席表
        remove(new QueryWrapper<>());
        // 批量保存新的坐席
        saveBatch(newAgentList);
    }
}
