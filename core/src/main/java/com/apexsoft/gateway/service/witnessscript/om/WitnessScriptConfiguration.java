package com.apexsoft.gateway.service.witnessscript.om;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("witness_script_configuration")
public class WitnessScriptConfiguration {
    @TableId
    private int id;
    private int bizType;
    private String bizTypeName;
    private int state;
    private String scriptModifyDate;
    private String scriptModifyTime;
    private String scriptConfig;
    private String bizCode;
}
