package com.apexsoft.gateway.service.dispatch.rule.request;

import com.apexsoft.dispatcher.dispatch.rule.RequestDispatchRule;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import com.apexsoft.util.ConcurrentDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 见证等待超时优先
 */
@Component("1002")
@Slf4j
public class WitnessWaitTimeoutDispatchRule extends RequestDispatchRule {
    private int requestTimeout = 10;

    @Override
    public void init() throws Exception {
        super.init();
        String ruleValue = dispatchRule.getDispatchRuleConfigEntity().getRuleValue();
        requestTimeout = 10; // 默认10分钟请求等待超时
        try {
            requestTimeout = Integer.parseInt(allRuleDict.get(ruleValue).getValue());
        } catch (Exception e) {
            log.error("requestTimeout is not a number", e);
        }

        log.info("requestTimeout init {}", requestTimeout);
    }

    @Override
    public int pushCompare(QueueWitnessRequest queueWitnessRequest1, QueueWitnessRequest queueWitnessRequest2, List<QueueWitnessRequest> queueWitnessRequests) {
        WitnessRequest request1 = queueWitnessRequest1.getWitnessRequest();
        WitnessRequest request2 = queueWitnessRequest2.getWitnessRequest();

        boolean request1Timout = isWaitTimeout(request1);
        boolean request2Timout = isWaitTimeout(request2);
        if (request1Timout) {
            if (request2Timout) {
                return 0;
            }
            return -FISRT_ELE_TO_BEHIND;
        }
        if (!request2Timout) {
            return 0;
        }
        return FISRT_ELE_TO_BEHIND;
    }

    private boolean isWaitTimeout (WitnessRequest witnessRequest) {
        try {
            int joinTime = Integer.parseInt(witnessRequest.getJoinTime());
            int nowTime = Integer.parseInt(ConcurrentDateUtil.now());
            return (nowTime - joinTime) > ((long) requestTimeout * 60);
        } catch (Exception e) {
            log.error("[{}]isWaitTimeout error", witnessRequest.getId(), e);
            return false;
        }
    }
}
