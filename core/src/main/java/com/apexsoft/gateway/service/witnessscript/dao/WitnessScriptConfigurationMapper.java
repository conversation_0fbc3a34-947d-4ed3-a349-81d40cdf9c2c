package com.apexsoft.gateway.service.witnessscript.dao;

import com.apexsoft.gateway.service.witnessscript.om.WitnessScriptConfiguration;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

@Mapper
public interface WitnessScriptConfigurationMapper extends BaseMapper<WitnessScriptConfiguration> {

        @Select("select * from witness_script_configuration where biz_type = #{bizTypeName, jdbcType=INTEGER}")
        WitnessScriptConfiguration selectById(int bizType);

        @Delete({
                        "<script>",
                        "DELETE FROM witness_script_configuration",
                        "WHERE biz_type = #{bizType, jdbcType=VARCHAR}",
                        "</script>"
        })
        int deleteById(int bizType);

        @Update({
                        "<script>",
                        "UPDATE witness_script_configuration",
                        "<set>",
                        "  <if test='bizTypeName != null'>",
                        "    biz_type_name = #{bizTypeName, jdbcType=VARCHAR},",
                        "  </if>",
                        "  <if test='bizCode != null'>",
                        "    biz_code = #{bizCode, jdbcType=VARCHAR},",
                        "  </if>",
                        "  <if test='state != null'>",
                        "    state = #{state, jdbcType=INTEGER},",
                        "  </if>",
                        "  <if test='scriptModifyDate != null'>",
                        "    script_modify_date = #{scriptModifyDate, jdbcType=VARCHAR},",
                        "  </if>",
                        "  <if test='scriptModifyTime != null'>",
                        "    script_modify_time = #{scriptModifyTime, jdbcType=VARCHAR},",
                        "  </if>",
                        "  <if test='scriptConfig != null'>",
                        "    script_config = #{scriptConfig, jdbcType=VARCHAR},",
                        "  </if>",
                        "</set>",
                        "WHERE biz_type = #{bizType, jdbcType=VARCHAR}",
                        "</script>"
        })
        int updateByType(WitnessScriptConfiguration witnessScriptConfiguration);

        @Insert("insert into witness_script_configuration " +
                        "(biz_type, biz_code, biz_type_name, state, script_modify_date, script_modify_time, script_config) "
                        +
                        "values " +
                        "(#{bizType}, #{bizCode}, #{bizTypeName}, #{state}, #{scriptModifyDate}, #{scriptModifyTime}, #{scriptConfig})")
        int newConfig(WitnessScriptConfiguration witnessScriptConfiguration) throws Exception;

        @Select({
                        "<script>",
                        "SELECT * FROM witness_script_configuration",
                        "<where>",
                        "  <if test='bizType != null'>",
                        "    AND biz_type = #{bizType, jdbcType=INTEGER}",
                        "  </if>",
                        "  <if test='bizCode != null and bizCode != \"\"'>",
                        "    AND biz_code = #{bizCode, jdbcType=VARCHAR}",
                        "  </if>",
                        "</where>",
                        "LIMIT 1",
                        "</script>"
        })
        WitnessScriptConfiguration selectByCondition(@Param("bizType") Integer bizType,
                        @Param("bizCode") String bizCode);
}
