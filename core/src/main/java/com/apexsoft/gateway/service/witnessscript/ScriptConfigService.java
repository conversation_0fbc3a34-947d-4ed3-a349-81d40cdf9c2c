package com.apexsoft.gateway.service.witnessscript;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import com.apexsoft.gateway.service.witnessscript.dao.WitnessScriptConfigurationMapper;
import com.apexsoft.gateway.service.witnessscript.om.WitnessScriptConfiguration;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

@Service
@Slf4j
public class ScriptConfigService extends ServiceImpl<WitnessScriptConfigurationMapper, WitnessScriptConfiguration> {

    @Autowired
    WitnessScriptConfigurationMapper witnessScriptConfigurationMapper;

    public CommonResponse getScriptConfig(Integer bizType, String bizCode) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try {
            WitnessScriptConfiguration configuration = witnessScriptConfigurationMapper.selectByCondition(bizType,
                    bizCode);
            JSONObject data = new JSONObject();
            data.put("configuration", configuration);
            response.setData(data);
        } catch (Exception e) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote(e.toString());
        }
        return response;
    }

    /**
     * 获取脚本配置列表（分页）
     *
     * @param state      状态
     * @param pageNumber 页码
     * @param pageSize   每页大小
     * @return 脚本配置列表
     */
    public CommonResponse getScriptConfigList(Integer state, Integer pageNumber, Integer pageSize) {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try {
            QueryWrapper<WitnessScriptConfiguration> queryWrapper = new QueryWrapper<>();
            if (state != null) {
                queryWrapper.eq("state", state);
            }

            Page<WitnessScriptConfiguration> page = new Page<>(pageNumber, pageSize);
            Page<WitnessScriptConfiguration> configPage = page(page, queryWrapper);

            response.setData(JSONObject.parseObject(JSONObject.toJSONString(configPage)));
        } catch (Exception e) {
            log.error("获取脚本配置列表失败", e);
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("获取脚本配置列表失败：" + e.getMessage());
        }
        return response;
    }

    public CommonResponse addScriptConfig(JSONObject params) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "添加成功");
        try {
            LocalTime currentTime = LocalTime.now();
            DateTimeFormatter formatterTime = DateTimeFormatter.ofPattern("HHmmss");
            String formattedTime = currentTime.format(formatterTime);
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyyMMdd");
            String formattedDate = currentDate.format(formatterDate);
            WitnessScriptConfiguration witnessScriptConfiguration = new WitnessScriptConfiguration();
            if (params.getInteger("bizType") != null && !StringUtils.isBlank(params.getInteger("bizType") + "")) {
                witnessScriptConfiguration.setBizType(params.getInteger("bizType"));
            }
            if (params.getString("bizCode") != null && !StringUtils.isBlank(params.getString("bizCode") + "")) {
                witnessScriptConfiguration.setBizCode(params.getString("bizCode"));
            }
            if (params.getString("scriptConfig") != null
                    && !StringUtils.isBlank(params.getString("scriptConfig") + "")) {
                witnessScriptConfiguration.setScriptConfig(params.getString("scriptConfig"));
            }

            if (params.getInteger("state") != null && !StringUtils.isBlank(params.getInteger("state") + "")) {
                witnessScriptConfiguration.setState(params.getInteger("state"));
            }
            if (params.getString("bizTypeName") != null
                    && !StringUtils.isBlank(params.getString("bizTypeName") + "")) {
                witnessScriptConfiguration.setBizTypeName(params.getString("bizTypeName"));
            }
            witnessScriptConfiguration.setScriptModifyDate(formattedDate);
            witnessScriptConfiguration.setScriptModifyTime(formattedTime);
            int configuration = witnessScriptConfigurationMapper.newConfig(witnessScriptConfiguration);
            if (configuration >= 0) {
                response.setCode(JSONResponse.CODE_SUCCESS);
            } else {
                response.setCode(JSONResponse.CODE_FAIL);
            }
        } catch (Exception e) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote(e.toString());
        }
        return response;
    }

    public CommonResponse deleteScriptConfig(int bizType) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "删除成功");
        try {
            int configuration = witnessScriptConfigurationMapper.deleteById(bizType);
            if (configuration >= 0) {
                response.setCode(JSONResponse.CODE_SUCCESS);
            } else {
                response.setCode(JSONResponse.CODE_FAIL);
            }
        } catch (Exception e) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote(e.toString());
        }
        return response;
    }

    public CommonResponse updateScriptConfig(JSONObject params) {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "添加成功");
        LocalTime currentTime = LocalTime.now();
        DateTimeFormatter formatterTime = DateTimeFormatter.ofPattern("HHmmss");
        String formattedTime = currentTime.format(formatterTime);
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = currentDate.format(formatterDate);
        try {
            WitnessScriptConfiguration witnessScriptConfiguration = new WitnessScriptConfiguration();
            if (params.getInteger("bizType") != null && !StringUtils.isBlank(params.getInteger("bizType") + "")) {
                witnessScriptConfiguration.setBizType(params.getInteger("bizType"));
            }
            if (params.getString("bizCode") != null && !StringUtils.isBlank(params.getString("bizCode") + "")) {
                witnessScriptConfiguration.setBizCode(params.getString("bizCode"));
            }
            if (params.getString("scriptConfig") != null
                    && !StringUtils.isBlank(params.getString("scriptConfig") + "")) {
                witnessScriptConfiguration.setScriptConfig(params.getString("scriptConfig"));
            }

            if (params.getInteger("state") != null && !StringUtils.isBlank(params.getInteger("state") + "")) {
                witnessScriptConfiguration.setState(params.getInteger("state"));
            }
            if (params.getString("bizTypeName") != null
                    && !StringUtils.isBlank(params.getString("bizTypeName") + "")) {
                witnessScriptConfiguration.setBizTypeName(params.getString("bizTypeName"));
            }
            witnessScriptConfiguration.setScriptModifyDate(formattedDate);
            witnessScriptConfiguration.setScriptModifyTime(formattedTime);
            int configuration = witnessScriptConfigurationMapper.updateByType(witnessScriptConfiguration);
            if (configuration >= 0) {
                response.setCode(JSONResponse.CODE_SUCCESS);
            } else {
                response.setCode(JSONResponse.CODE_FAIL);
            }
        } catch (Exception e) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote(e.toString());
        }
        return response;
    }
}
