package com.apexsoft.gateway.service.dispatch.queue;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.dispatcher.dispatch.Queue;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WitnessQueueService {

    @Autowired
    private Queue queue;

    /**
     * 根据条件查询队列情况，不分页
     * @param clientId
     * @param clientName
     * @param org
     * @return
     */
    public CommonResponse queryQueue (String clientId, String clientName, String org) {
        int count = 3;
        while (count > 0 && queue.isNeedReSort()) {
            // 还没完成排序，先等等
            try {
                Thread.sleep(1000);
            } catch (Exception e) {
                e.printStackTrace();
            }
            count--;
        }
        // @todo 看下是否可以将所有泛型全部去掉，客户数据或坐席数据放在上层维护。如果改成这个逻辑，这里返回的数据要注意加上客户数据，获取坐席数据的地方也要注意加上坐席数据
        List<QueueWitnessRequest> queueWitnessRequests = queue.getSortedRequests();
        List<Object> filterQueueWitnessRequests = queueWitnessRequests
                .stream()
                .filter(queueWitnessRequest -> {
                    WitnessRequest witnessRequest = (WitnessRequest) queueWitnessRequest.getWitnessRequest();
                    if (StringUtils.hasLength(clientId) && !witnessRequest.getClientId().contains(clientId)) {
                        return false;
                    }
                    if (StringUtils.hasLength(clientName) && !witnessRequest.getClientName().contains(clientName)) {
                        return false;
                    }
                    if (StringUtils.hasLength(org) && !(";" + org + ";").contains(";" + witnessRequest.getOrg() + ";")) {
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
        CommonResponse commonResponse = new CommonResponse();
        JSONObject data = new JSONObject();
        data.put("records", filterQueueWitnessRequests);
        commonResponse.setData(data);
        return commonResponse;
    }

    /**
     * 置顶
     * @param requestIdStr
     * @return
     */
    public CommonResponse topRequest (String requestIdStr) {
        CommonResponse result = new CommonResponse();
        long requestId = -1;
        try {
            requestId = Long.parseLong(requestIdStr);
        } catch (Exception ignored) {}
        String note = queue.topRequest(requestId);
        if (StringUtils.hasLength(note)) {
            result.setCode(-2);
            result.setNote(note);
            return result;
        }
        return result;
    }
}
