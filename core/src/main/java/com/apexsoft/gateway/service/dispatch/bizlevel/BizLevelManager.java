package com.apexsoft.gateway.service.dispatch.bizlevel;

import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.service.dispatch.AbstractManager;
import com.apexsoft.gateway.service.dispatch.bizlevel.dao.BizLevelMapper;
import com.apexsoft.gateway.service.dispatch.bizlevel.om.BizLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

// @todo AbstractManager的抽象方法还没完全实现
@Service
@Slf4j
public class BizLevelManager extends AbstractManager<Integer, BizLevel> {

    @Autowired
    private BizLevelMapper bizLevelMapper;

    @Override
    protected String name() {
        return "bizLevel";
    }

    @Override
    protected List<BizLevel> getAll() {
        return bizLevelMapper.getAll();
    }

    @Override
    protected Integer getKey(BizLevel bizLevel) {
        return bizLevel.getBizLevel();
    }

    public Integer getBizLevelScore (Integer bizLevel) {
        return getBizLevelScore(bizLevel, 0);
    }

    public Integer getBizLevelScore (Integer bizLevel, int defaultValue) {
        return allElements.get(bizLevel) == null ? defaultValue : allElements.get(bizLevel).getScore();
    }

    @Override
    protected CommonResponse insertOne(BizLevel bizLevel) {
        return null;
    }

    @Override
    protected CommonResponse delete(String... keys) {
        return null;
    }

    @Override
    protected CommonResponse updateOne(BizLevel bizLevel) {
        return null;
    }
}
