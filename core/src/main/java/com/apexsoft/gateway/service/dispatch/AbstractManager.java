package com.apexsoft.gateway.service.dispatch;

import com.apexsoft.gateway.model.CommonResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Getter
@Slf4j
public abstract class AbstractManager<K, V> {

    // 数据库表的所有数据放在内存中缓存
    protected final Map<K, V> allElements = new HashMap<>();

    protected abstract String name();
    protected abstract List<V> getAll();
    protected abstract K getKey(V v);

    /**
     * 新增
     * @param v 新增的数据
     * @return 返回新增的数量，大于0才会更新内存里的数据
     */
    protected abstract CommonResponse insertOne(V v);
    protected abstract CommonResponse delete(String... keys);
    protected abstract CommonResponse updateOne(V v);

    @PostConstruct
    public void init() {
        initLocal();
    }
    private synchronized void initLocal () {
        log.info("load {}", name());
        List<V> all = getAll();
        allElements.clear();
        for (V v: all) {
            allElements.put(getKey(v), v);
        }
    }

    public V get(K k, V defaultValue) {
        V value = allElements.get(k);
        return value == null ? defaultValue : value;
    }

    private CommonResponse commonMethod (Function<Void, CommonResponse> function) {
        CommonResponse result = function.apply(null);
        if (result != null && result.getCode() > 0) {
            initLocal();
        }
        return  result;
    }

    public CommonResponse addOne(V v) {
        return this.commonMethod(n -> insertOne(v));
    }

    public CommonResponse remove(String... keys) {
        return this.commonMethod(n -> delete(keys));
    }

    public CommonResponse update(V v) {
        return this.commonMethod(n -> updateOne(v));
    }
}
