package com.apexsoft.gateway.service;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.base.constant.Constant;
import com.apexsoft.gateway.common.utils.TLSSigAPIv2;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sts.v20180813.StsClient;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenRequest;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenResponse;
import com.tencentcloudapi.trtc.v20190722.TrtcClient;
import com.tencentcloudapi.trtc.v20190722.models.*;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.*;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

import javax.annotation.PostConstruct;

@Service
public class TencentCloudService {

    @Value("${tc-ai.trtc-SDKAppID}")
    private long SDKAppID;

    @Value("${tc-ai.trtc-SDKSecretKey}")
    private String SDKSecretKey;

    @Value("${tc-ai.SecretId}")
    private String SecretId;

    @Value("${tc-ai.SecretKey}")
    private String SecretKey;

    @Value("${tc-ai.appid}")
    private Long appid;

    // 视频布局常量
    private static final Long VIDEO_WIDTH = 972L;
    private static final Long VIDEO_HEIGHT = 968L;
    private static final Long MAIN_VIEW_HEIGHT = 632L;
    private static final Long SPACING = 2L;
    private static final Long SUB_VIEW_WIDTH = 262L;

    private Credential credential;

    @PostConstruct
    public void init() {
        this.credential = new Credential(SecretId, SecretKey);
    }

    public String generateUserSign(String userId, long expire) {
        TLSSigAPIv2 api = new TLSSigAPIv2(SDKAppID, SDKSecretKey);
        return api.genUserSig(userId, expire);
    }

    public String startCloudRecording(String roomId, String connectId, String userId,String recordMode) throws Exception {
        String recorderId = "recorder_" + roomId;
        String recorderSign = generateUserSign(recorderId, 86400);

        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("trtc.tencentcloudapi.com");

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        TrtcClient client = new TrtcClient(credential, "ap-shanghai", clientProfile);

        CreateCloudRecordingRequest req = new CreateCloudRecordingRequest();
        req.setSdkAppId(SDKAppID);
        req.setRoomId(roomId);
        req.setUserId(recorderId);
        req.setUserSig(recorderSign);

        // 设置录制参数
        RecordParams recordParams = new RecordParams();
        recordParams.setRecordMode(2L);
        req.setRecordParams(recordParams);
        req.setRoomIdType(1L);

        // 设置混流布局参数
        MixLayoutParams mixLayoutParams = new MixLayoutParams();
        MixTranscodeParams mixTranscodeParams = new MixTranscodeParams();

        // 设置视频参数
        VideoParams videoParams = new VideoParams();
        videoParams.setFps(15L);
        videoParams.setBitRate(500000L);
        videoParams.setGop(10L);

        // 根据不同的录制模式设置不同的布局
        if (recordMode != null && Constant.RECORD_MODE_0.equals(recordMode)) {
            // 模式0：三分屏布局
            configureMode0Layout(videoParams, mixLayoutParams, mixTranscodeParams, connectId, userId);
        } else if (Constant.RECORD_MODE_1.equals(recordMode)) {
            // 模式1：上下分屏布局
            configureMode1Layout(videoParams, mixLayoutParams, mixTranscodeParams, connectId, userId);
        } else {
            // 默认模式：单一视图
            configureDefaultLayout(videoParams, mixTranscodeParams, mixLayoutParams,connectId);
        }

        req.setMixTranscodeParams(mixTranscodeParams);

        // 设置水印
        WaterMark[] waterMarks = new WaterMark[2];

        WaterMark waterMark1 = new WaterMark();
        waterMark1.setWaterMarkType(2L);
        WaterMarkTimestamp waterMarkTimestamp = new WaterMarkTimestamp();
        waterMarkTimestamp.setPos(0L);
        waterMarkTimestamp.setTimeZone(8L);
        waterMark1.setWaterMarkTimestamp(waterMarkTimestamp);
        waterMarks[0] = waterMark1;

        WaterMark waterMark2 = new WaterMark();
        waterMark2.setWaterMarkType(1L);
        WaterMarkChar waterMarkChar = new WaterMarkChar();
        waterMarkChar.setTop(0L);
        waterMarkChar.setLeft(0L);
        waterMarkChar.setWidth(300L);
        waterMarkChar.setHeight(100L);
        waterMarkChar.setFontSize(18L);
        waterMarkChar.setFontColor("0xFF0000");
        waterMarkChar.setChars("顶点软件");
        waterMark2.setWaterMarkChar(waterMarkChar);
        waterMarks[1] = waterMark2;

        mixLayoutParams.setWaterMarkList(waterMarks);
        req.setMixLayoutParams(mixLayoutParams);

        // 设置存储参数
        StorageParams storageParams = new StorageParams();
        CloudVod cloudVod = new CloudVod();
        TencentVod tencentVod = new TencentVod();
        tencentVod.setProcedure("LongVideoPreset");
        tencentVod.setStorageRegion("ap-shanghai");
        tencentVod.setClassId(0L);
        tencentVod.setSubAppId(appid);
        cloudVod.setTencentVod(tencentVod);
        storageParams.setCloudVod(cloudVod);
        req.setStorageParams(storageParams);

        CreateCloudRecordingResponse resp = client.CreateCloudRecording(req);
        return resp.getTaskId();
    }

    public void stopCloudRecording(String taskId) throws Exception {
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("trtc.tencentcloudapi.com");

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        TrtcClient client = new TrtcClient(credential, "ap-shanghai", clientProfile);

        DeleteCloudRecordingRequest req = new DeleteCloudRecordingRequest();
        req.setSdkAppId(SDKAppID);
        req.setTaskId(taskId);

        client.DeleteCloudRecording(req);
    }

    public void dismissRoom(Long roomId) throws Exception {
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("trtc.tencentcloudapi.com");

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        TrtcClient client = new TrtcClient(credential, "ap-guangzhou", clientProfile);

        DismissRoomRequest req = new DismissRoomRequest();
        req.setSdkAppId(SDKAppID);
        req.setRoomId(roomId);

        client.DismissRoom(req);
    }

    public JSONObject searchMedia(String roomId) throws Exception {
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("vod.tencentcloudapi.com");

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        VodClient client = new VodClient(credential, "ap-guangzhou", clientProfile);

        SearchMediaRequest req = new SearchMediaRequest();
        req.setSubAppId(appid);

        ZoneOffset zoneOffset = ZoneOffset.of("+08:00");
        LocalDate today = LocalDate.now();

        TimeRange timeRange = new TimeRange();
        DateTimeFormatter isoFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");

        OffsetDateTime after = today.atStartOfDay().atOffset(zoneOffset);
        timeRange.setAfter(after.format(isoFormatter));

        OffsetDateTime before = today.atTime(23, 59, 59).atOffset(zoneOffset);
        timeRange.setBefore(before.format(isoFormatter));
        req.setCreateTime(timeRange);

        String[] trtcRoomIds = {roomId};
        req.setTrtcRoomIds(trtcRoomIds);

        SearchMediaResponse resp = client.SearchMedia(req);
        JSONObject result = new JSONObject();

        if (resp.getTotalCount() > 0) {
            MediaInfo[] mediaInfo = resp.getMediaInfoSet();
            result.put("mediaUrl", mediaInfo[0].getBasicInfo().getMediaUrl());
            result.put("coverUrl", mediaInfo[0].getBasicInfo().getCoverUrl());
        }

        return result;
    }

    public JSONObject getTempToken() throws Exception {
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("sts.tencentcloudapi.com");

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        StsClient client = new StsClient(credential, "ap-shanghai", clientProfile);

        String policy = "{"
                + "    \"statement\": ["
                + "        {"
                + "            \"action\": ["
                + "                \"asr:*\""
                + "            ],"
                + "            \"effect\": \"allow\","
                + "            \"resource\": ["
                + "                \"*\""
                + "            ]"
                + "        }"
                + "    ],"
                + "    \"version\": \"2.0\""
                + "}";

        GetFederationTokenRequest req = new GetFederationTokenRequest();
        req.setName("asrtem");
        req.setDurationSeconds(7200L);
        req.setPolicy(URLEncoder.encode(policy, StandardCharsets.UTF_8.toString()));

        GetFederationTokenResponse resp = client.GetFederationToken(req);

        JSONObject result = new JSONObject();
        result.put("asrToken", resp.getCredentials().getToken());
        result.put("asrSecretId", resp.getCredentials().getTmpSecretId());
        result.put("asrSecretKey", resp.getCredentials().getTmpSecretKey());
        result.put("appId", appid);

        return result;
    }


/**
     * 配置模式0的布局（三分屏）
     */
    private void configureMode0Layout(VideoParams videoParams, MixLayoutParams mixLayoutParams,
                                     MixTranscodeParams mixTranscodeParams, String connectId, String userId) {
        // 设置视频参数
        videoParams.setWidth(VIDEO_WIDTH);
        videoParams.setHeight(VIDEO_HEIGHT);
        mixTranscodeParams.setVideoParams(videoParams);

        // 设置布局模式
        mixLayoutParams.setMixLayoutMode(4L);
        MixLayout[] mixLayouts = new MixLayout[3];

        // 主视图布局
        MixLayout mixLayout1 = new MixLayout();
        mixLayout1.setTop(0L);
        mixLayout1.setLeft(0L);
        mixLayout1.setWidth(VIDEO_WIDTH);
        mixLayout1.setHeight(MAIN_VIEW_HEIGHT);
        mixLayout1.setUserId(connectId);
        mixLayout1.setMediaId(0L);
        mixLayout1.setRenderMode(2L);
        mixLayouts[0] = mixLayout1;

        // 子视图布局
        MixLayout mixLayout2 = new MixLayout();
        mixLayout2.setTop(MAIN_VIEW_HEIGHT + SPACING);
        mixLayout2.setLeft(0L);
        mixLayout2.setWidth(SUB_VIEW_WIDTH);
        mixLayout2.setHeight(VIDEO_HEIGHT - MAIN_VIEW_HEIGHT - SPACING);
        mixLayout2.setUserId(userId);
        mixLayout2.setRenderMode(3L);
        mixLayouts[1] = mixLayout2;

        // 第三个视图布局
        MixLayout mixLayout3 = new MixLayout();
        mixLayout3.setTop(MAIN_VIEW_HEIGHT + SPACING);
        mixLayout3.setLeft(SUB_VIEW_WIDTH + 38L);
        mixLayout3.setWidth(VIDEO_WIDTH - SUB_VIEW_WIDTH - 38L);
        mixLayout3.setHeight(VIDEO_HEIGHT - MAIN_VIEW_HEIGHT - SPACING);
        mixLayout3.setMediaId(1L);
        mixLayout3.setRenderMode(2L);
        mixLayouts[2] = mixLayout3;

        mixLayoutParams.setMixLayoutList(mixLayouts);
    }

    /**
     * 配置模式1的布局（上下分屏）
     */
    private void configureMode1Layout(VideoParams videoParams, MixLayoutParams mixLayoutParams,
                                     MixTranscodeParams mixTranscodeParams, String connectId, String userId) {
        // 设置视频参数
        videoParams.setWidth(VIDEO_WIDTH);
        videoParams.setHeight(VIDEO_HEIGHT);
        mixTranscodeParams.setVideoParams(videoParams);

        // 设置布局模式
        mixLayoutParams.setMixLayoutMode(4L);
        MixLayout[] mixLayouts = new MixLayout[2];

        // 主视图布局
        MixLayout mixLayout1 = new MixLayout();
        mixLayout1.setTop(0L);
        mixLayout1.setLeft(0L);
        mixLayout1.setWidth(VIDEO_WIDTH);
        mixLayout1.setHeight(MAIN_VIEW_HEIGHT);
        mixLayout1.setUserId(connectId);
        mixLayout1.setRenderMode(2L);
        mixLayouts[0] = mixLayout1;

        // 子视图布局
        MixLayout mixLayout2 = new MixLayout();
        mixLayout2.setTop(MAIN_VIEW_HEIGHT);
        mixLayout2.setLeft(0L);
        mixLayout2.setWidth(VIDEO_WIDTH);
        mixLayout2.setHeight(SUB_VIEW_WIDTH);
        mixLayout2.setMediaId(1L);
        mixLayout2.setRenderMode(2L);
        mixLayouts[1] = mixLayout2;

        mixLayoutParams.setMixLayoutList(mixLayouts);
    }

    /**
     * 配置默认布局（单一视图）
     */
    private void configureDefaultLayout(VideoParams videoParams, MixTranscodeParams mixTranscodeParams,
                                       MixLayoutParams mixLayoutParams,String connectId) {
        // 设置视频参数
        videoParams.setWidth(640L);
        videoParams.setHeight(480L);
        mixTranscodeParams.setVideoParams(videoParams);

        // 设置布局模式
        mixLayoutParams.setMixLayoutMode(1L);


        // 设置布局模式
        mixLayoutParams.setMixLayoutMode(4L);
        MixLayout[] mixLayouts = new MixLayout[1];

        // 主视图布局
        MixLayout mixLayout1 = new MixLayout();
        mixLayout1.setTop(0L);
        mixLayout1.setLeft(0L);
        mixLayout1.setWidth(640L);
        mixLayout1.setHeight(480L);
        mixLayout1.setUserId(connectId);
        mixLayout1.setRenderMode(2L);
        mixLayouts[0] = mixLayout1;

        mixLayoutParams.setMixLayoutList(mixLayouts);
    }
}
