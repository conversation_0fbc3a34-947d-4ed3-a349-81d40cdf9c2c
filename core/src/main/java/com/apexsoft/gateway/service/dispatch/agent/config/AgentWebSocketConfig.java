package com.apexsoft.gateway.service.dispatch.agent.config;

import com.apexsoft.gateway.service.dispatch.agent.handler.AgentWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
@Slf4j
public class AgentWebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private AgentWsHandshakeInterceptor agentWsHandshakeInterceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(agentWebSocketHandler(), "/witness-agent")
                .addInterceptors(agentWsHandshakeInterceptor)
                .setAllowedOrigins("http://localhost:3000", "https://ygt.apexsoft.com.cn");
    }

    @Bean
    public AgentWebSocketHandler agentWebSocketHandler () {
        return new AgentWebSocketHandler();
    }
}
