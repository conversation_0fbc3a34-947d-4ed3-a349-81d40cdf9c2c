package com.apexsoft.gateway.service.qualitycheck;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.QualityCheckRequest;
import com.apexsoft.gateway.service.qualitycheck.dao.QualityCheckMapper;
import com.apexsoft.gateway.service.qualitycheck.om.WitnessQualityCheck;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class QualityCheckService {

    @Autowired
    private QualityCheckMapper qualityCheckMapper;

    public CommonResponse query () {
        List<WitnessQualityCheck> list = qualityCheckMapper.selectList(null);
        CommonResponse response = new CommonResponse();
        JSONObject data = new JSONObject();
        data.put("records", list);
        response.setData(data);
        return response;
    }

    public CommonResponse update (QualityCheckRequest qualityCheckRequest) {
        List<WitnessQualityCheck> witnessQualityChecks = qualityCheckRequest.getValues();
        witnessQualityChecks.forEach(witnessQualityCheck -> {
            UpdateWrapper<WitnessQualityCheck> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("code", witnessQualityCheck.getCode());
            qualityCheckMapper.update(witnessQualityCheck, updateWrapper);
        });
        return new CommonResponse();
    }
}
