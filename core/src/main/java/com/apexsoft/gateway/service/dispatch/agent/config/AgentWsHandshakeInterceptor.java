package com.apexsoft.gateway.service.dispatch.agent.config;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.gateway.service.dispatch.agent.handler.AgentWebSocketHandler;
import com.apexsoft.live.session.UserAuthenticateSession;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Map;

@Component
public class AgentWsHandshakeInterceptor implements HandshakeInterceptor {

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        if (request instanceof ServletServerHttpRequest) {
            // 获取 HttpServletRequest
            HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();

            // 从 HttpServletRequest 中获取信息
            AuthUser<JSONObject> user = UserAuthenticateSession.getUser(servletRequest);
            String id = user.getUserId();
            attributes.put(AgentWebSocketHandler.AGENT_ID, id); // 将信息存储到 WebSocket 会话属性中
            HttpSession session = servletRequest.getSession();
            // 将 HttpSession 存储到 WebSocket 会话属性中
            attributes.put(AgentWebSocketHandler.HTTP_SESSION, session);
        }
        return true; // 返回 true 表示允许握手
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {

    }
}
