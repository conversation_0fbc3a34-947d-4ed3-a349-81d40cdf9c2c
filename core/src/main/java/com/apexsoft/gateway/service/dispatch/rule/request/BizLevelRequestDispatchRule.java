package com.apexsoft.gateway.service.dispatch.rule.request;

import com.apexsoft.dispatcher.dispatch.rule.RequestDispatchRule;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.service.dispatch.WitnessUtilService;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("1005")
@Slf4j
public class BizLevelRequestDispatchRule extends RequestDispatchRule {

//    @Override
//    public String ruleCode() {
//        return "1005";
//    }

    @Autowired
    private WitnessUtilService witnessUtilService;

    @Override
    public int pushCompare(QueueWitnessRequest o1, QueueWitnessRequest o2, List<QueueWitnessRequest> requests) {
        WitnessRequest witnessRequest1 = o1.getWitnessRequest();
        WitnessRequest witnessRequest2 = o2.getWitnessRequest();
        String bizLevel1 = witnessUtilService.getBizLevel(witnessRequest1.getBizCode());
        String bizLevel2 = witnessUtilService.getBizLevel(witnessRequest2.getBizCode());
        return commonCompare(bizLevel1, bizLevel2);
    }
}
