package com.apexsoft.gateway.service.dispatch.rule;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.dispatcher.om.DispatchRuleConfigEntity;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.DispatchRuleValueRequest;
import com.apexsoft.gateway.service.dispatch.dao.DispatchRuleCodeMapper;
import com.apexsoft.gateway.service.dispatch.dao.DispatchRuleValueMapper;
import com.apexsoft.gateway.service.dispatch.rule.om.DispatchRuleValue;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class DispatchRuleConfigService extends ServiceImpl<DispatchRuleValueMapper, DispatchRuleValue> implements IService<DispatchRuleValue> {

    @Autowired
    private DispatchRuleCodeMapper dispatchRuleCodeMapper;

    public CommonResponse getAll () {
        CommonResponse response = new CommonResponse();
        try {
            List<DispatchRuleConfigEntity> allRules = dispatchRuleCodeMapper.getAll();
            JSONObject data = new JSONObject();
            data.put("allRules", allRules);
            response.setData(data);
        } catch (Exception e) {
            log.error("get all rule error", e);
            response.setCode(-1);
            response.setNote(e.getMessage());
        }
        return response;
    }

    public CommonResponse updateRules (DispatchRuleValueRequest request) {
        List<DispatchRuleValue> dispatchRuleValueList = request.getValues();
        CommonResponse response = new CommonResponse();
        for (DispatchRuleValue dispatchRuleValue: dispatchRuleValueList) {
            UpdateWrapper<DispatchRuleValue> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("rule_code", dispatchRuleValue.getRuleCode());
            updateWrapper.set("rule_value", dispatchRuleValue.getRuleValue());
            if (!update(updateWrapper)) {
                response.setCode(-1);
                response.setNote("规则[" + dispatchRuleValue.getRuleCode() + "]更新失败");
            }
        }
        return response;
    }
}
