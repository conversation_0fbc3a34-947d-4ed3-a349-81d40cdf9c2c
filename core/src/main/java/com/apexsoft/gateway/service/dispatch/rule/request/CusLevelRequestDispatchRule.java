package com.apexsoft.gateway.service.dispatch.rule.request;

import com.apexsoft.dispatcher.dispatch.rule.RequestDispatchRule;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.service.dispatch.WitnessUtilService;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("1004")
@Slf4j
public class CusLevelRequestDispatchRule extends RequestDispatchRule {

//    @Override
//    public String ruleCode() {
//        return "1004";
//    }

    @Autowired
    private WitnessUtilService witnessUtilService;

    @Override
    public int pushCompare(QueueWitnessRequest o1, QueueWitnessRequest o2, List<QueueWitnessRequest> requests) {
        WitnessRequest request1 = o1.getWitnessRequest();
        WitnessRequest request2 = o2.getWitnessRequest();

        int cusLevel1 = witnessUtilService.getCusLevel(request1);
        int cusLevel2 = witnessUtilService.getCusLevel(request2);
        return commonCompare("" + cusLevel1, "" + cusLevel2);
    }
}
