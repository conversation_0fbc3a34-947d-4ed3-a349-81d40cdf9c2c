package com.apexsoft.gateway.service.dispatch.agent.handler;

import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.dispatch.agent.AgentService;
import com.apexsoft.gateway.service.dispatch.agent.config.AgentEvents;
import com.apexsoft.gateway.service.dispatch.agent.config.HeartbeatScheduler;
import com.apexsoft.gateway.service.dispatch.agent.om.Message;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import javax.servlet.http.HttpSession;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class AgentWebSocketHandler extends TextWebSocketHandler {

    public final static String HTTP_SESSION = "HTTP_SESSION";
    public final static String AGENT_ID = "agentId";
    // 多端登录冲突返回码
    private final static int CONFLICT_CODE = 4001;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private AgentService agentService;

    @Autowired
    private HeartbeatScheduler heartbeatScheduler;

    // 存储用户会话
    private final static Map<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        try {
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        } catch (Exception e) {
            log.error("Failed to initialize objectMapper: {}", e.getMessage());
            throw new RuntimeException("objectMapper initialization failed", e);
        }
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
//        String token = extractToken(session);
//        String userId = validateToken(token);
        // 获取存储在会话属性中的信息
        String userId = (String) session.getAttributes().get(AGENT_ID);

        if (userId != null) {
            String note= agentService.agentOnLine(userId, session.getId());
            if (!"".equals(note)) {
                session.close(CloseStatus.POLICY_VIOLATION.withReason(note));
                return;
            }
            WebSocketSession oldWsSession = userSessions.get(userId);
            if (oldWsSession != null) {
                // 旧的会话要断开连接
                oldWsSession.close(new CloseStatus(CONFLICT_CODE).withReason("坐席在其他地方登录"));
            }
            userSessions.put(userId, session);
            session.getAttributes().put("userId", userId);
            // 获取 HttpSession
            HttpSession httpSession = (HttpSession) session.getAttributes().get(HTTP_SESSION);
            heartbeatScheduler.registerSession(session, httpSession);
            log.info("User {} connected {}", userId, session.getId());
        } else {
            session.close(CloseStatus.POLICY_VIOLATION.withReason("会话已过期"));
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        Message msg = objectMapper.readValue(payload, Message.class);
        String agentId = (String) session.getAttributes().get("userId");
        if (AgentEvents.accept.getEventName().equals(msg.getEventType())) {
            JSONResponse response = agentService.accept(agentId);
            Message responseMsg = new Message(msg.getEventType(), response);
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(responseMsg)));
        } else if (AgentEvents.reject.getEventName().equals(msg.getEventType())) {
            agentService.reject(agentId);
        } else if (AgentEvents.result.getEventName().equals(msg.getEventType())) {
            Map<String, String> jsonObject = (Map<String, String>) msg.getData();
            String result = jsonObject.get("result");
            String reason = jsonObject.get("reason");
            agentService.endServe(agentId, result, reason);
        } else if (AgentEvents.changeStatus.getEventName().equals(msg.getEventType())) {
            Map<String, String> jsonObject = (Map<String, String>) msg.getData();
            String status = jsonObject.get("status");
            JSONResponse response = agentService.changeStatus(agentId, status);
            Message responseMsg = new Message(msg.getEventType(), response);
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(responseMsg)));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        super.handleTransportError(session, exception);
        log.error("agent ws error", exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String userId = (String) session.getAttributes().get("userId");
        if (userId != null) {
            userSessions.remove(userId);
            agentService.agentOffLine(userId);
        }
        // 注销会话
        heartbeatScheduler.unregisterSession(session);
        log.info("agent User {} disconnected {}", userId, session.getId());
    }

    public void sendMsg (String agentId, Message message) throws Exception {
        WebSocketSession webSocketSession = userSessions.get(agentId);
        if (webSocketSession != null) {
            webSocketSession.sendMessage(new TextMessage(objectMapper.writeValueAsString(message)));
            return;
        }
        log.error("no client to send msg: agentId, {}; msg, {}", agentId, message.getData());
    }

    private String extractToken(WebSocketSession session) {
        // 从WebSocket握手请求中提取token
        return session.getUri().getQuery().replace("token=", "");
    }

    private String validateToken(String token) {
        try {
            Claims user = jwtUtil.parseJWT(token);
            return user.getId();
        } catch (Exception e) {
            log.error("Invalid token", e);
            return null;
        }
    }
}
