package com.apexsoft.gateway.service.dispatch.agent.config;

import lombok.Getter;

/**
 * 坐席事件类型（前后端通信用的事件类型）
 */
@Getter
public enum AgentEvents {
    changeStatus("changeStatus"),
    // 通知坐席，队列人数发生了变化
    queueChange("queueChange"),
    // 向坐席发送呼叫
    call("call"),
    // 向坐席发送呼叫终止
    endCall("endCall"),
    // 坐席发送接听消息
    accept("accept"),
    // 坐席发送拒绝接听消息
    reject("reject"),
    // 坐席发送见证结果
    result("result");

    private final String eventName;
    AgentEvents(String eventName) {
        this.eventName = eventName;
    }

}
