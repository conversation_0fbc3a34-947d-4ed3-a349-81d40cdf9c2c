package com.apexsoft.gateway.service.dispatch;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.dispatcher.dispatch.rule.DispatcherMethod;
import com.apexsoft.dispatcher.om.AgentSession;
import com.apexsoft.dispatcher.om.DispatchRuleConfigEntity;
import com.apexsoft.dispatcher.om.QueueWitnessRequest;
import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.service.dispatch.agent.AgentService;
import com.apexsoft.gateway.service.dispatch.agent.om.Agent;
import com.apexsoft.gateway.service.dispatch.agenttech.AgentTechManager;
import com.apexsoft.gateway.service.dispatch.agenttech.om.AgentTech;
import com.apexsoft.gateway.service.dispatch.client.ClientService;
import com.apexsoft.gateway.service.dispatch.client.om.WitnessRequest;
import com.apexsoft.gateway.service.dispatch.dao.DispatchRuleCodeMapper;
import com.apexsoft.gateway.service.witnessProperty.PropertyService;
import com.apexsoft.gateway.service.witnessProperty.om.WitnessProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class DispatcherMethodImpl implements DispatcherMethod<Agent>, Constant {

    // 呼叫超时时间，单位秒，从数据库配置获取
    private int callTimeout = -1;
    // 拒绝超时时间，坐席拒绝后多久才能继续被该客户呼叫，单位秒，从数据库配置获取
    private int rejectTimeout = -1;

    @Autowired
    private PropertyService propertyService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private AgentTechManager agentTechManager;

    @Autowired
    private ClientService clientService;

    @Autowired
    private DispatchRuleCodeMapper dispatchRuleCodeMapper;

    @Override
    public List<DispatchRuleConfigEntity> getAll() throws Exception {
        return dispatchRuleCodeMapper.getAll();
    }

    @Override
    public int getCallTimeout() {
//        return callTimeout;
        if (callTimeout > 0) {
            return callTimeout;
        }
        callTimeout = 45;
        WitnessProperty property = propertyService.getByCode("callTimeout");
//        WitnessProperty property = propertyManager.get("callTimeout", null);
        if (property != null) {
            String callTimeoutStr = property.getValue();
            try {
                callTimeout = Integer.parseInt(callTimeoutStr);
            } catch (Exception e) {
                log.error("callTimeout 系统参数值不是数值：{}", callTimeoutStr);
            }
        }
        return callTimeout;
    }

    @Override
    public int getRejectTimeout() {
//        return rejectTimeout;
        if (rejectTimeout > 0) {
            return rejectTimeout;
        }
        rejectTimeout = 20;
        WitnessProperty property = propertyService.getByCode("rejectTimeout");
//        WitnessProperty property = propertyManager.get("rejectTimeout", null);
        if (property != null) {
            String rejectTimeoutStr = property.getValue();
            try {
                rejectTimeout = Integer.parseInt(rejectTimeoutStr);
            } catch (Exception e) {
                log.error("rejectTimeout 系统参数值不是数值：{}", rejectTimeoutStr);
            }
        }
        return rejectTimeout;
    }

    @Override
    public boolean checkAgentCanServe(QueueWitnessRequest queueWitnessRequest, AgentSession<Agent> agentSession, Date nowDate) {
        WitnessRequest request = queueWitnessRequest.getWitnessRequest();
        if (request == null) {
            log.info("请求为空");
            return false;
        }
        Agent agent = agentSession.getAgentInfo();
        if (agent == null) {
            log.info("{}坐席信息为空", agentSession.getAgentId());
            return false;
        }
        // 判断坐席状态，不是空闲状态则不允许服务 @todo 这里是否需要放在单次允许服务的判断里？
        if (agent.getStatus() != AGENT_STATUS_FREE) {
            log.info("{}坐席当前状态不是空闲状态", agentSession.getAgentId());
            return false;
        }
        // 判断指定坐席，如果存在指定坐席，非指定坐席则无法为他服务
        String appointAgentId = queueWitnessRequest.getAppointAgentId();
        if (StringUtils.hasLength(appointAgentId)) {
            // 指定坐席
            return appointAgentId.equals(agentSession.getAgentId());
        }

        // @todo 这里还差业务等级的匹配没有实现，另外日志记录是否另外找个格式记录
        // 根据技能组判断是否允许服务
        int type = request.getBizType();
        int cusLevel = request.getCusLevel();
        String techs = agent.getTechs();
        if (!StringUtils.hasLength(techs)) {
            log.info("坐席[{}]没有技能组", agentSession.getAgentId());
            return false;
        }
        log.info("检查坐席[{}]的技能组[{}]是否匹配客户信息[type: {}, cusLevel: {}]",
                agentSession.getAgentId(), techs, type, cusLevel);
        String[] techList = techs.split(";");
        for (String tech: techList) {
            AgentTech agentTech = agentTechManager.get(tech, null);
            if (agentTech == null) {
                log.info("技能组编码[{}]不存在", tech);
                continue;
            }
            boolean isTypeRight = type == agentTech.getTechType();
            boolean isCusLevelRight = (";" + agentTech.getCusLevel() + ";").contains(";" + cusLevel + ";");
            if (isTypeRight && isCusLevelRight) {
                log.info("当前技能[{}]与客户信息[type: {}, cusLevel: {}]匹配成功", agentTech, type, cusLevel);
                return true;
//            } else {
//                log.info("当前技能[{}]与客户信息[type: {}, cusLevel: {}]不匹配", agentTech, type, cusLevel);
            }
        }
        // 所有技能组都检查了，没有匹配成功的，说明该坐席无法为该客户服务
        log.info("坐席[{}]的技能组[{}]不能匹配客户信息[type: {}, cusLevel: {}]",
                agentSession.getAgentId(), techs, type, cusLevel);
        return false;
    }

    @Override
    public void onQueueCountChanged(int count) {
        agentService.sendQueueCountToAllAgent(count);
    }

    @Override
    public void haveNoAgentOnline(QueueWitnessRequest queueWitnessRequest) {
        // 无坐席。修改见证请求状态为“无坐席”及相关数据
        WitnessRequest request = queueWitnessRequest.getWitnessRequest();
        request.setState(-2);
        clientService.witnessFailed(request);
    }

//    @Override
//    public AgentSession getBestAgent(QueueWitnessRequest<WitnessRequest> queueWitnessRequest, List<AgentSession> canServeAndFreeNotCallAgents) {
//        // 可以调用如下方法调用接口的default方法
//        return DispatcherMethod.super.getBestAgent(queueWitnessRequest, canServeAndFreeNotCallAgents);
//    }

    @Override
    public void sendCallInfoToAgent(QueueWitnessRequest queueWitnessRequest, String callAgentId) {
        WitnessRequest request = queueWitnessRequest.getWitnessRequest();
        JSONObject info = new JSONObject();
        // 此次见证请求的id
        info.put("witnessRequestId", request.getId());
        // 客户唯一id，可以是客户号，也可以是客户证件号、手机号等数据
        // 和witness/client/addWitnessReques接口的clientId一致
        info.put("clientId", request.getClientId());
        // 该笔业务请求的id，用于发送给座席端，座席端查询相关客户信息业务数据时使用
        // 和witness/client/addWitnessReques接口的bizRequestId一致
        info.put("bizRequestId", request.getBizRequestId());
        // @todo 前端呼叫超时的逻辑还没实现
        // 呼叫开始时间，格式：yyyyMMddHHmmss
        info.put("callTime", queueWitnessRequest.getCallingTime());
        // 呼叫超时时间，单位秒
        info.put("callTimeout", getCallTimeout());
        // 发送呼叫给坐席
        agentService.sendCallInfo(callAgentId, info);
    }

    @Override
    public void onWitnessServeStart(QueueWitnessRequest queueWitnessRequest) {
        // 修改请求表状态为接收（撮合成功）
        clientService.accept(queueWitnessRequest);
    }

//    @Override
//    public void onWitnessServeEnd(AgentSession agentSession, String result, String reason) {
//        // 更新表格数据
//        clientService.witnessResult(agentSession.getServeRequestId(), result, reason);
//    }
}
