package com.apexsoft.gateway.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.DispatchRuleValueRequest;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.dispatch.agenttech.om.AgentTech;
import com.apexsoft.gateway.service.dispatch.rule.DispatchRuleConfigService;
import com.apexsoft.gateway.service.dispatch.rule.om.DispatchRuleValue;
import com.apexsoft.gateway.service.opratelog.OperateLogService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collection;

/**
 * 派单规则
 */
@RequestMapping("/witness/dispatch")
@Controller
@Slf4j
public class DispatchRuleConfigController implements Constant {

    @Autowired
    private DispatchRuleConfigService dispatchRuleConfigService;
    @Autowired
    private OperateLogService operateLogService;

    @RequestMapping("/queryAllRules")
    @ResponseBody
    @ApiOperation(value = "查询所有派单规则", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "查询成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse queryAllRules()
            throws Exception {
        return dispatchRuleConfigService.getAll();
    }

    @RequestMapping("/updateRules")
    @ResponseBody
    @ApiOperation(value = "修改派单规则", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "修改成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse updateRules(
            @ApiParam(name = "values", value = "派单规则值请求对象", required = true)
            @RequestBody
            DispatchRuleValueRequest values)
            throws Exception {
        JSONResponse response = dispatchRuleConfigService.updateRules(values);
        operateLogService.addNewLog(MENU_ZXPDGZ, ACTION_MODIFY, JSON.toJSONString(values), response);
        return response;
    }
}
