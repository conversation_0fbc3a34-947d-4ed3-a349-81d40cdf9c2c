package com.apexsoft.gateway.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.dispatch.agenttech.AgentTechManager;
import com.apexsoft.gateway.service.dispatch.agenttech.om.AgentTech;
import com.apexsoft.gateway.service.opratelog.OperateLogService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collection;

@RequestMapping("/witness/agenttech")
@Controller
@Slf4j
public class AgentTechController implements Constant {

    @Autowired
    private AgentTechManager agentTechManager;

    @Autowired
    private OperateLogService operateLogService;

    @RequestMapping("/add")
    @ResponseBody
    @ApiOperation(value = "新增技能组", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "新增成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse createAgentTech(
            @ApiParam(name = "agentTech", value = "技能组对象", required = true)
            @RequestBody
            AgentTech agentTech)
            throws Exception {
        JSONResponse response = agentTechManager.addOne(agentTech);
        operateLogService.addNewLog(MENU_ZXJNZ, ACTION_ADD, JSON.toJSONString(agentTech), response);
        return response;
    }

    @RequestMapping("/remove")
    @ResponseBody
    @ApiOperation(value = "删除技能组", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "删除成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse removeAgentTech(
            @ApiParam(name = "agentTech", value = "技能组对象", required = true)
            @RequestBody
            AgentTech agentTech)
            throws Exception {
        String[] techCodes = agentTech.getTechCode().split(";");
        JSONResponse response = agentTechManager.remove(techCodes);
        operateLogService.addNewLog(MENU_ZXJNZ, ACTION_REMOVE, JSON.toJSONString(agentTech), response);
        return response;
    }

    @RequestMapping("/update")
    @ResponseBody
    @ApiOperation(value = "修改技能组", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "修改成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse updateAgentTech(
            @ApiParam(name = "agentTech", value = "技能组对象", required = true)
            @RequestBody
            AgentTech agentTech)
            throws Exception {
        JSONResponse response = agentTechManager.update(agentTech);
        operateLogService.addNewLog(MENU_ZXJNZ, ACTION_MODIFY, JSON.toJSONString(agentTech), response);
        return response;
    }

    @RequestMapping("/queryAll")
    @ResponseBody
    @ApiOperation(value = "查询所有技能组", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "查询成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse queryAllAgentTech()
            throws Exception {
        Collection<AgentTech> allAgentTech = agentTechManager.getAllElements().values();
        CommonResponse result = new CommonResponse();
        JSONObject data = new JSONObject();
        data.put("agentTechAll", JSON.parseArray(JSON.toJSONString(allAgentTech)));
        result.setData(data);
        return result;
    }
}
