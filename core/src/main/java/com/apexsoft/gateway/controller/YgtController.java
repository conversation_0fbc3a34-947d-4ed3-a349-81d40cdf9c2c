package com.apexsoft.gateway.controller;


import com.alibaba.fastjson.JSONObject;
import com.apex.ams.client.dynamic.ServerReflectionLoader;
import com.apexsoft.gateway.aas.modules.index.service.DefaultServiceHandler;
import com.apexsoft.gateway.common.redis.RedisDao;
import com.apexsoft.gateway.common.redis.RedisStringDao;
import com.apexsoft.gateway.common.utils.LdapUtil;
import com.apexsoft.gateway.common.utils.PingYinUtil;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@RequestMapping("/test")
@Controller
@Slf4j
public class YgtController {
    private DefaultServiceHandler defaultServiceHandler = new DefaultServiceHandler();




    @Autowired
    RedisDao redisDao;
    @Autowired
    RedisStringDao redisStringDao;
    @Autowired
    LdapUtil ldapUtil;

    @Autowired
    CommonService commonService;


    @RequestMapping("/clearServiceCache")
    @ResponseBody
    public void clearServiceCache() {
        //ServerReflectionLoader.getInstance().refresh(namespace, serviceId); //这个是刷新单个服务
        ServerReflectionLoader.getInstance().clear(); //这个是全刷
    }

    @RequestMapping("/getRedisKey")
    @ResponseBody
    public String getRedisKey(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", request, response);
        String key = jsonData.getString("key");
        return redisStringDao.getValue(key);
    }

    @RequestMapping("/cxCoworkInfo")
    @ResponseBody
    public JSONResponse cxCoworkInfo(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject result = new JSONObject();
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        result = commonService.cxCoworkInfo(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>0){
            JSONObject coworkInfo = new JSONObject();
            coworkInfo.put("khxxInfo",result.getJSONObject("khxxInfo"));
            coworkInfo.put("ywqqList",result.getJSONArray("ywqqList"));
            response.setData(coworkInfo);
        }
        return response;
    }


    @RequestMapping("/getBusinessData")
    @ResponseBody
    public JSONResponse getBusinessData(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject result = new JSONObject();
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String enywqqid = jsonData.getString("enywqqid");
        String pagelength = StringUtils.isBlank(jsonData.getString("pagelength")) ? "10" : jsonData.getString("pagelength");
        String pageno = StringUtils.isBlank(jsonData.getString("pageno")) ? "1" : jsonData.getString("pageno");
        if(StringUtils.isBlank(enywqqid)){
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("ywqqid不能为空");
            return response;
        }
        JSONObject paramYwqq=new JSONObject();
        paramYwqq.put("ywqqid",enywqqid);
//        paramYwqq.put("pagelength",pagelength);
//        paramYwqq.put("pageno",pageno);
        result = commonService.queryYwqq(paramYwqq);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>0){
            JSONObject data = new JSONObject();
            if(result.get("isSecondary") != null){
                JSONObject ywqqJson = result.getJSONObject("ywqqJson");
                if(ywqqJson.containsKey("khmc")){
                    ywqqJson.put("khpymc", PingYinUtil.getPingYin(ywqqJson.getString("khmc")," | ",","));
                }
                data.put("BusinessData", ywqqJson);
                data.put("BusinessDataSupplement", result.getJSONArray("records").getJSONObject(0));
            }else {
                String ywqqStr = result.getString("req_data");
                JSONObject ywqqJson = JSONObject.parseObject(ywqqStr);
                if(ywqqJson.containsKey("khmc")){
                    ywqqJson.put("khpymc", PingYinUtil.getPingYin(ywqqJson.getString("khmc")," | ",","));
                }
                data.put("BusinessData", ywqqJson);
            }
            if(data.getJSONObject("BusinessData").containsKey("fqr")){
                JSONObject getTeller = commonService.cxgyxx(data.getJSONObject("BusinessData").getString("fqr"));
                if(getTeller.getInteger("code") > 0 && getTeller.getJSONArray("records").size() > 0){
                    data.put("TellerInfo", getTeller.getJSONArray("records").getJSONObject(0));
                }
            }
            response.setData(data);
        }
        return response;
    }

    @RequestMapping("/getBusinessSummary")
    @ResponseBody
    public JSONResponse getBusinessSummary(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject result = new JSONObject();
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String ywqqid = jsonData.getString("ywqqid");
        String pageno = jsonData.getString("pageno");
        String pagelength = jsonData.getString("pagelength");
        if(StringUtils.isBlank(ywqqid)){
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("ywqqid不能为空");
            return response;
        }
        if(StringUtils.isBlank(pageno)){
            jsonData.put("pageno","1");
        }
        if(StringUtils.isBlank(pagelength)){
            jsonData.put("pagelength","10");
        }
        result = commonService.queryYwqqSj(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>0){
            response.setData(result.getJSONArray("records"));
        }
        return response;
    }

    @RequestMapping("/mpsVerificationApplication")
    @ResponseBody
    public JSONResponse mpsVerificationApplication(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject result = new JSONObject();
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        result = commonService.execGawsqForPhoto(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>0){
            JSONObject data = new JSONObject();
            data.put("jgsm",result.getString("jgsm"));
            data.put("cljg",result.getString("cljg"));
            data.put("xb",result.getString("xb"));
            data.put("sfzh",result.getString("sfzh"));
            data.put("filepath",result.getString("filepath"));
            data.put("xm",result.getString("xm"));
            data.put("yzrq",result.getString("yzrq"));
            data.put("id",result.getString("id"));
            data.put("gqts",result.getString("gqts"));
            data.put("bdfz",result.getString("bdfz"));
            data.put("hzcxjg",result.getString("hzcxjg"));
            response.setData(data);
        }
        return response;
    }

    @RequestMapping("/mpsVerificationResult")
    @ResponseBody
    public JSONResponse mpsVerificationResult(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject result = new JSONObject();
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        result = commonService.queryGawsqFkjg(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>0){
            response.setData(result.getJSONArray("records"));
        }
        return response;
    }

    @RequestMapping("/queryDictionary")
    @ResponseBody
    public JSONResponse queryDictionary(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String fldm = jsonData.getString("fldm");
        if(StringUtils.isBlank(fldm)){
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("fldm不能为空");
            return response;
        }
        JSONObject result = new JSONObject();
        result = commonService.queryDictionary(jsonData);
        JSONObject data = new JSONObject();
        data.put("dictionary", result);
        response.setData(data);
        return response;
    }

    @RequestMapping("/queryOrgById")
    @ResponseBody
    public JSONResponse queryOrgById(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String gyid = jsonData.getString("gyid");
        if(StringUtils.isBlank(gyid)){
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("gyid不能为空");
            return response;
        }
        JSONObject result = new JSONObject();
        result = commonService.queryOrg(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>0){
            JSONObject data = new JSONObject();
            data.put("org", result.getJSONArray("records"));
            response.setData(data);
        }
        return response;
    }

    @RequestMapping("/getYxrwmxByYwqqid")
    @ResponseBody
    public JSONResponse getYxrwmxByYwqqid(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String ywqqid = jsonData.getString("ywqqid");
        if(StringUtils.isBlank(ywqqid)){
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("ywqqid不能为空");
            return response;
        }
        JSONObject result = new JSONObject();
        result = commonService.getYxrwmxByYwqqid(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>0){
            JSONObject data = new JSONObject();
            data.put("records", result.getJSONArray("records"));
            response.setData(data);
        }
        return response;
    }

    @RequestMapping("/getZsrwjgByYwqqid")
    @ResponseBody
    public JSONResponse getZsrwjgByYwqqid(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String ywqqid = jsonData.getString("ywqqid");
        if(StringUtils.isBlank(ywqqid)){
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("ywqqid不能为空");
            return response;
        }
        JSONObject result = new JSONObject();
        result = commonService.getZsrwjgByYwqqid(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>0){
            JSONObject data = new JSONObject();
            data.put("records", result.getJSONArray("records"));
            response.setData(data);
        }
        return response;
    }

    /*
    return code 0 同一个人 2不是同一个人 小于0表示比对失败
     */
    @RequestMapping("/comparePhoto")
    @ResponseBody
    public JSONResponse comparePhoto(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        JSONObject result = new JSONObject();
        result = commonService.comparePhoto(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>=0){
            response.setData(result.getJSONObject("result"));
        }
        return response;
    }

    @RequestMapping("/cxywqqyscs")
    @ResponseBody
    public JSONResponse cxywqqyscs(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        JSONObject result = new JSONObject();
        result = commonService.cxywqqyscs(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>=0){
            response.setData(result.getJSONArray("records"));
        }
        return response;
    }

    @RequestMapping("/cxkhdrywfqcsjthyy")
    @ResponseBody
    public JSONResponse cxkhdrywfqcsjthyy(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        JSONObject result = new JSONObject();
        result = commonService.cxkhdrywfqcsjthyy(jsonData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if(response.getCode()>=0){
            response.setData(result.getJSONArray("records"));
        }
        return response;
    }

    @RequestMapping("/modifyAccept")
    @ResponseBody
    public JSONResponse modifyAccept(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "修改成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String ywqqid = jsonData.getString("ywqqid");
        JSONObject updateData = jsonData.getJSONObject("data");
        if(StringUtils.isBlank(ywqqid)){
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("ywqqid不能为空");
            return response;
        }
        JSONObject result = new JSONObject();
        result = commonService.modifyReqData(ywqqid,updateData);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
//        if(response.getCode()>0){
//            JSONObject data = new JSONObject();
//            data.put("businessSummary", result.getJSONArray("records"));
//            response.setData(data);
//        }
        return response;
    }
}
