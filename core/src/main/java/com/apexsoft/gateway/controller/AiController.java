package com.apexsoft.gateway.controller;

import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.model.QualityCheckRequest;
import com.apexsoft.gateway.service.qualitycheck.QualityCheckService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/witness/ai")
@Controller
@Slf4j
public class AiController {

    @Autowired
    private QualityCheckService qualityCheckService;

    @RequestMapping("/queryAllQualityCheck")
    @ResponseBody
    @ApiOperation(value = "查询所有质检参数", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "查询成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse queryAllQualityCheck()
            throws Exception {
        return qualityCheckService.query();
    }

    @RequestMapping("/updateQualityCheck")
    @ResponseBody
    @ApiOperation(value = "修改质检参数", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "修改成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse updateRules(
            @ApiParam(name = "values", value = "质检参数请求对象", required = true)
            @RequestBody
            QualityCheckRequest values)
            throws Exception {
        return qualityCheckService.update(values);
    }
}
