package com.apexsoft.gateway.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.apex.ams.client.dynamic.ServerReflectionLoader;
import com.apexsoft.gateway.aas.common.session.UserSession;
import com.apexsoft.gateway.aas.modules.index.model.AuthUser;
import com.apexsoft.gateway.aas.modules.index.service.DefaultServiceHandler;
import com.apexsoft.gateway.base.constant.Constant;
import com.apexsoft.gateway.common.redis.RedisDao;
import com.apexsoft.gateway.common.redis.RedisStringDao;
import com.apexsoft.gateway.common.utils.LdapUtil;
import com.apexsoft.gateway.common.utils.TLSSigAPIv2;
import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.TencentCloudService;
import com.apexsoft.live.session.UserAuthenticateContext;
import com.apexsoft.live.session.UserAuthenticateSession;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sts.v20180813.StsClient;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenRequest;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenResponse;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.MediaInfo;
import com.tencentcloudapi.vod.v20180717.models.SearchMediaRequest;
import com.tencentcloudapi.vod.v20180717.models.SearchMediaResponse;
import com.tencentcloudapi.vod.v20180717.models.TimeRange;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Random;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.trtc.v20190722.TrtcClient;
import com.tencentcloudapi.trtc.v20190722.models.*;

import static com.apexsoft.gateway.common.utils.MediaDownloader.downloadAndSave;

@RequestMapping("/user")
@Controller
@Slf4j
public class UserController {
    private DefaultServiceHandler defaultServiceHandler = new DefaultServiceHandler();



    @Autowired
    RedisDao redisDao;
    @Autowired
    RedisStringDao redisStringDao;

    @Autowired
    private JwtUtil jwtUtil;
    @Autowired
    LdapUtil ldapUtil;
    @Autowired
    private TencentCloudService tencentCloudService;

    @Value("${tc-ai.trtc-SDKAppID}")
    private long SDKAppID;
    @Value("${tc-ai.trtc-SDKSecretKey}")
    private String SDKSecretKey;
    @Value("${tc-ai.usersign-expire}")
    private long expire;
    @Value("${tc-ai.SecretId}")
    private String SecretId;
    @Value("${tc-ai.SecretKey}")
    private String SecretKey;
    @Value("${tc-ai.appid}")
    private Long appid;

  // 视频布局常量
  private static final Long VIDEO_WIDTH = 972L;
  private static final Long VIDEO_HEIGHT = 968L;
  private static final Long MAIN_VIEW_HEIGHT = 632L;
  private static final Long SPACING = 2L;
  private static final Long SUB_VIEW_WIDTH = 262L;
    private Credential credential; // 声明一个共享的 Credential 实例
    @PostConstruct
    public void init() {
        // 在初始化时创建 Credential 实例
        this.credential =  new Credential(SecretId, SecretKey);
    }


    @RequestMapping(value = "/checkAuth",name = "校验是否过期")
    @ResponseBody
    public JSONResponse checkAuth(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        int id = (int)user.getId();
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");

        return response;
    }

    @RequestMapping(value = "/gen-trtc-sign",name = "生成trtc签名")
    @ResponseBody
    public JSONResponse gentrtcsign(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        String id = user.getUserId();
        String userSign = tencentCloudService.generateUserSign(id, expire);
        JSONObject result = new JSONObject();
        result.put("userSign",userSign);
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        response.setData(result);
        return response;
    }

    @RequestMapping(value = "/start-record",name = "开始录制")
    @ResponseBody
    public JSONResponse startRecord(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String roomId = jsonData.getString("roomId");
        String connectId = jsonData.getString("connectId");
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        String id = user.getUserId();

        String taskId = tencentCloudService.startCloudRecording(roomId, connectId, id, Constant.RECORD_MODE_0);

        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject result = new JSONObject();
        result.put("taskId", taskId);
        response.setData(result);
        return response;
    }

    @RequestMapping(value = "/stop-record",name = "开始录制")
    @ResponseBody
    public JSONResponse stopRecord(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String taskId = jsonData.getString("taskId");
        tencentCloudService.stopCloudRecording(taskId);
        return new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
    }

    @RequestMapping(value = "/dismiss-room",name = "解散房间")
    @ResponseBody
    public JSONResponse DismissRoom(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        Long roomId = jsonData.getLong("roomId");
        try {
            tencentCloudService.dismissRoom(roomId);
            return new CommonResponse(JSONResponse.CODE_SUCCESS, "解散房间成功");
        } catch (Exception e) {
            return new CommonResponse(JSONResponse.CODE_SUCCESS, "解散房间成功");
        }
    }
    @RequestMapping(value = "/me",name = "柜员信息")
    @ResponseBody
    public JSONResponse me(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        try{

            JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
            AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);

            JSONObject userinfo = user.getUser().clone();
            String photo = UserSession.getUserPhoto(user.getId()+"");
            userinfo.put("photo", photo);
            userinfo.put("simulation", user.isSimulation());
            userinfo.put("id", user.getId());
            userinfo.put("userid", user.getUserId());
            userinfo.put("SDKAppID", SDKAppID);
            response.setData(userinfo);
        }catch (Exception e){
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询失败:"+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/socket-token",name = "生成websocket对话的token")
    @ResponseBody
    public JSONResponse getSocketToken(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
//
        JSONObject result = new JSONObject();
//        result.put("userSign",userSign);
        AuthUser<JSONObject> user = UserAuthenticateSession.getUser(httpServletRequest);
        String id = user.getUserId();
        // 生成一个介于min和max之间的随机整数
        Random random = new Random();
        int randomNumber = random.nextInt(999 - 100 + 1) + 100;
        String wsUserId=id+"_454399_"+randomNumber;
        TLSSigAPIv2 api = new TLSSigAPIv2(SDKAppID, SDKSecretKey);

        String customerSign= api.genUserSig(wsUserId, 180*86400);
        String customerToken=jwtUtil.createJWT(wsUserId,id);
        String userToken=jwtUtil.createJWT(id,wsUserId);
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");

        result.put("userToken",userToken);
        result.put("customerToken",customerToken);
        result.put("customerSign",customerSign);
        result.put("customerId",wsUserId);
        response.setData(result);
        return response;
    }

    @RequestMapping(value = "/search-media",name = "查询并下载录制好的文件")
    @ResponseBody
    public JSONResponse SearchMedia(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String roomId = jsonData.getString("roomId");

        JSONObject mediaInfo = tencentCloudService.searchMedia(roomId);
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        response.setData(mediaInfo);
        return response;
    }

    @RequestMapping(value = "/ttem",name = "临时访问凭证")
    @ResponseBody
    public JSONResponse ttem(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        try {
            JSONObject result = tencentCloudService.getTempToken();
            CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
            response.setData(result);
            return response;
        } catch (Exception e) {
            return new CommonResponse(JSONResponse.CODE_FAIL, "查询失败:" + e.getMessage());
        }
    }




}
