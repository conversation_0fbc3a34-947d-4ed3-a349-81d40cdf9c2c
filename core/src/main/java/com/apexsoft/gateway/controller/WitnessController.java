package com.apexsoft.gateway.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.aas.modules.index.service.DefaultServiceHandler;
import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.common.utils.TLSSigAPIv2;
import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.CommonService;
import com.apexsoft.gateway.service.dispatch.client.ClientService;
import com.apexsoft.gateway.service.opratelog.OperateLogService;
import com.apexsoft.gateway.service.witnessdictionary.DictionaryService;
import com.apexsoft.gateway.service.witnessProperty.PropertyService;
import com.apexsoft.gateway.service.witnessProperty.om.WitnessProperty;
import com.apexsoft.gateway.service.witnessscript.ScriptConfigService;
import com.apexsoft.gateway.service.coworkscript.CoworkScriptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;

/**
 * @todo 该接口需要增加会话控制，客户端要如何和见证系统进行会话控制？----使用jwt
 */
@RequestMapping("/witness/client")
@Controller
@Slf4j
public class WitnessController implements Constant {
    private DefaultServiceHandler defaultServiceHandler = new DefaultServiceHandler();

    @Autowired
    ClientService clientService;

    @Autowired
    ScriptConfigService scriptConfigService;

    @Autowired
    CoworkScriptService coworkScriptService;

    @Autowired
    DictionaryService dictionaryService;

    @Autowired
    CommonService commonService;

    @Autowired
    private JwtUtil jwtUtil;
    @Value("${tc-ai.trtc-SDKAppID}")
    private long SDKAppID;
    @Value("${tc-ai.trtc-SDKSecretKey}")
    private String SDKSecretKey;

    @Autowired
    PropertyService propertyService;

    @Autowired
    private OperateLogService operateLogService;

    /**
     * 新增见证请求
     *
     * @param clientId   客户唯一id
     * @param bizType    此次业务类型
     * @param test       是否为测试数据，测试数据只能用于新增见证请求，因为没有业务数据，坐席会接不起来
     * @param org        客户营业部（用于测试数据的时候必送，否则送了也没意义）
     * @param clientName 客户姓名（用于测试数据的时候必送，否则送了也没意义）
     * @param bizType    业务类型（用于测试数据的时候必送，否则送了也没意义）
     * @param bizCode    业务代码（用于测试数据的时候必送，否则送了也没意义）
     * @param cusLevel   客户等级（用于测试数据的时候必送，否则送了也没意义）
     * @return
     */
    @RequestMapping("/addWitnessRequest")
    @ResponseBody
    public JSONResponse addWitnessRequest(
            String clientId,
            // int org,
            int bizRequestId,
            @RequestParam(name = "test", required = false, defaultValue = "") String test,
            @RequestParam(name = "org", required = false, defaultValue = "") String org,
            @RequestParam(name = "clientName", required = false, defaultValue = "") String clientName,
            @RequestParam(name = "bizType", required = false, defaultValue = "1") int bizType,
            @RequestParam(name = "bizCode", required = false, defaultValue = "") String bizCode,
            @RequestParam(name = "cusLevel", required = false, defaultValue = "1") int cusLevel) {
        return clientService.addWitnessRequest(clientId, bizRequestId, test, org, clientName, bizType, bizCode,
                cusLevel);
    }

    /**
     * 获取见证请求的当前状态，可轮询该接口
     *
     * @param requestId
     * @return
     */
    @RequestMapping("/getWitnessRequest")
    @ResponseBody
    public JSONResponse getWitnessRequest(String requestId) {
        return clientService.getWitnessRequest(requestId);
    }

    /**
     * 获取客户端ws腾讯连接要用的数据
     *
     * @param clientId
     * @return
     */
    @RequestMapping("/getClientSocketInfo")
    @ResponseBody
    public JSONResponse getClientSocketInfo(String clientId) {
        TLSSigAPIv2 api = new TLSSigAPIv2(SDKAppID, SDKSecretKey);
        String customerSign = api.genUserSig(clientId, 180 * 86400);
        String customerToken = jwtUtil.createJWT(clientId, "");
        CommonResponse result = new CommonResponse(1, "获取成功");
        JSONObject data = new JSONObject();
        data.put("customerToken", customerToken);
        data.put("customerSign", customerSign);
        data.put("sdkAppId", SDKAppID);
        result.setData(data);
        return result;
    }

    /**
     * 取消见证请求
     *
     * @param requestId
     * @return
     */
    @RequestMapping("/cancelWitnessRequest")
    @ResponseBody
    public JSONResponse cancelWitnessRequest(String requestId) {
        return clientService.cancelWitnessRequest(requestId);
    }

    @RequestMapping("/getScriptConfig")
    @ResponseBody
    public JSONResponse getScriptConfig(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        Integer bizType = jsonData.getInteger("bizType");
        String bizCode = jsonData.getString("bizCode");

        if ((bizType == null || StringUtils.isBlank(bizType + "")) && StringUtils.isBlank(bizCode)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("bizType和bizCode不能同时为空");
            return response;
        }
        response = scriptConfigService.getScriptConfig(bizType, bizCode);
        return response;
    }

    /**
     * 获取脚本配置列表（分页）
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 脚本配置列表
     * @throws Exception 异常
     */
    @RequestMapping("/getScriptConfigList")
    @ResponseBody
    public JSONResponse getScriptConfigList(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        Integer state = jsonData.getInteger("state");
        Integer pageNumber = jsonData.getInteger("pageNumber");
        Integer pageSize = jsonData.getInteger("pageSize");

        if (pageNumber == null || pageSize == null) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("pageNumber和pageSize不能为空");
            return response;
        }

        response = scriptConfigService.getScriptConfigList(state, pageNumber, pageSize);
        return response;
    }

    @RequestMapping("/updateScriptConfig")
    @ResponseBody
    public JSONResponse updateScriptConfig(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "修改成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        int bizType = jsonData.getInteger("bizType");
        if (StringUtils.isBlank(bizType + "")) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("bizType不能为空");
            return response;
        }
        response = scriptConfigService.updateScriptConfig(jsonData);
        operateLogService.addNewLog(MENU_JZHSPZ, ACTION_MODIFY, jsonData, response);
        return response;
    }

    @RequestMapping("/deleteScriptConfig")
    @ResponseBody
    public JSONResponse deleteScriptConfig(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "删除成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        int bizType = jsonData.getInteger("bizType");
        if (StringUtils.isBlank(bizType + "")) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("bizType不能为空");
            return response;
        }
        response = scriptConfigService.deleteScriptConfig(bizType);
        operateLogService.addNewLog(MENU_JZHSPZ, ACTION_REMOVE, jsonData, response);
        return response;
    }

    @RequestMapping("/addScriptConfig")
    @ResponseBody
    public JSONResponse addScriptConfig(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        int bizType = jsonData.getInteger("bizType");
        if (StringUtils.isBlank(bizType + "")) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("bizType不能为空");
            return response;
        }
        response = scriptConfigService.addScriptConfig(jsonData);
        operateLogService.addNewLog(MENU_JZHSPZ, ACTION_ADD, jsonData, response);
        return response;
    }

    @RequestMapping("/modifyWitness")
    @ResponseBody
    public JSONResponse modifyWitness(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "修改成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        Long id = jsonData.getLong("id");
        // String markerData = jsonData.getString("markerData");
        if (StringUtils.isBlank(id + "")) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("id不能为空");
            return response;
        }
        response = clientService.modifyWitness(jsonData);
        return response;
    }

    @RequestMapping("/getWitness")
    @ResponseBody
    public JSONResponse getWitness(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "修改成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        Long id = jsonData.getLong("id");
        if (StringUtils.isBlank(id + "")) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("id不能为空");
            return response;
        }
        response = clientService.getWitness(id);
        return response;
    }

    @RequestMapping("/getWitnessCount")
    @ResponseBody
    public JSONResponse getWitnessCount(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "修改成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        if (StringUtils.isBlank(jsonData.getString("serveAgentId"))) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("serveAgentId不能为空");
            return response;
        }
        String serveAgentId = jsonData.getString("serveAgentId");
        response = clientService.getWitnessCount(serveAgentId);
        return response;
    }

    @RequestMapping("/downLoadOfEsb")
    @ResponseBody
    public JSONResponse mpsVerificationResult(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        String filepath = httpServletRequest.getParameter("filepath");
        if (StringUtils.isBlank(filepath)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("filepath不能为空");
            return response;
        }
        try (OutputStream outputStream = httpServletResponse.getOutputStream()) {
            JSONObject download = commonService.downloadFile(outputStream, filepath);
            outputStream.flush();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return response;
    }

    @RequestMapping({ "image" })
    @ResponseBody
    public JSONObject uploadImage(HttpServletRequest request, HttpServletResponse response) {
        JSONObject result = commonService.uploadImage(request);
        return result;
    }

    @RequestMapping("/getWitnessDict")
    @ResponseBody
    public JSONResponse getWitnessDict(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String dicCode = jsonData.getString("dicCode");
        String[] dicCodes = null;
        String tableName = jsonData.getString("tableName");
        String[] tableNames = StringUtils.isNotBlank(tableName) ? tableName.split(";") : null;
        // 安全地处理JSON数组转换
        if (jsonData.containsKey("dicCodes") && !jsonData.getJSONArray("dicCodes").isEmpty()) {
            dicCodes = jsonData.getJSONArray("dicCodes").toArray(new String[0]);
        }

        if (StringUtils.isBlank(dicCode) && (dicCodes == null || dicCodes.length == 0)
                && StringUtils.isBlank(tableName)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("dicCode/dicCodes/tableName不能同时为空");
            return response;
        }
        response = dictionaryService.getWitnessDict(dicCode, dicCodes, tableNames);
        return response;
    }

    @RequestMapping("/getWitnessDictList")
    @ResponseBody
    public JSONResponse getWitnessDictList(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String dicCode = jsonData.getString("dicCode");
        Integer pageNumber = jsonData.getInteger("pageNumber");
        Integer pageSize = jsonData.getInteger("pageSize");
        if (pageNumber == null || pageSize == null) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("pageNumber和pageSize不能为空");
            return response;
        }
        response = dictionaryService.getWitnessDictList(dicCode, pageNumber, pageSize);
        return response;
    }

    /**
     * 获取系统参数
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 系统参数信息
     * @throws Exception 异常
     */
    @RequestMapping("/getProperty")
    @ResponseBody
    public JSONResponse getProperty(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String code = jsonData.getString("code");
        String name = jsonData.getString("name");

        if (StringUtils.isBlank(code) && StringUtils.isBlank(name)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("参数代码(code)或参数名称(name)不能同时为空");
            return response;
        }

        WitnessProperty property = null;
        if (StringUtils.isNotBlank(code)) {
            property = propertyService.getByCode(code);
        } else if (StringUtils.isNotBlank(name)) {
            property = propertyService.getByName(name);
        }

        if (property == null) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("系统参数不存在");
            return response;
        }

        JSONObject data = new JSONObject();
        data.put("records", property);
        response.setData(data);
        return response;
    }

    /**
     * 查询系统参数列表
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 系统参数列表
     * @throws Exception 异常
     */
    @RequestMapping("/listProperties")
    @ResponseBody
    public JSONResponse listProperties(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        WitnessProperty queryParam = new WitnessProperty();
        if (jsonData.containsKey("code")) {
            queryParam.setCode(jsonData.getString("code"));
        }
        if (jsonData.containsKey("name")) {
            queryParam.setName(jsonData.getString("name"));
        }
        if (jsonData.containsKey("value")) {
            queryParam.setValue(jsonData.getString("value"));
        }

        List<WitnessProperty> properties = propertyService.listProperties(queryParam);

        JSONObject data = new JSONObject();
        data.put("records", properties);
        data.put("total", properties.size());
        response.setData(data);

        return response;
    }

    /**
     * 添加系统参数
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 操作结果
     * @throws Exception 异常
     */
    @RequestMapping("/addProperty")
    @ResponseBody
    public JSONResponse addProperty(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "添加成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String code = jsonData.getString("code");
        String name = jsonData.getString("name");

        if (StringUtils.isBlank(code) || StringUtils.isBlank(name)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("参数代码(code)和参数名称(name)不能为空");
            return response;
        }

        WitnessProperty property = new WitnessProperty();
        property.setCode(code);
        property.setName(name);
        property.setValue(jsonData.getString("value"));
        property.setDescription(jsonData.getString("description"));

        boolean result = propertyService.addProperty(property);
        if (!result) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("添加系统参数失败，可能是参数代码或名称已存在");
        }

        operateLogService.addNewLog(MENU_XTCSGL, ACTION_ADD, jsonData, response);
        return response;
    }

    /**
     * 更新系统参数
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 操作结果
     * @throws Exception 异常
     */
    @RequestMapping("/updateProperty")
    @ResponseBody
    public JSONResponse updateProperty(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "更新成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String code = jsonData.getString("code");
        if (StringUtils.isBlank(code)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("参数代码(code)不能为空");
            return response;
        }

        WitnessProperty property = new WitnessProperty();
        property.setCode(code);

        if (jsonData.containsKey("name")) {
            property.setName(jsonData.getString("name"));
        }
        if (jsonData.containsKey("value")) {
            property.setValue(jsonData.getString("value"));
        }
        if (jsonData.containsKey("description")) {
            property.setDescription(jsonData.getString("description"));
        }

        boolean result = propertyService.updateProperty(property);
        if (!result) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("更新系统参数失败，可能是参数不存在或新名称已被使用");
        }

        operateLogService.addNewLog(MENU_XTCSGL, ACTION_MODIFY, jsonData, response);
        return response;
    }

    /**
     * 删除系统参数
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 操作结果
     * @throws Exception 异常
     */
    @RequestMapping("/deleteProperty")
    @ResponseBody
    public JSONResponse deleteProperty(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "删除成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String[] codes = null;
        try {
            codes = jsonData.getJSONArray("code").toArray(new String[0]);
        } catch (Exception e) {
            log.error("解析code参数失败", e);
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("code参数格式不正确，应为字符串数组");
            return response;
        }

        if (codes == null || codes.length == 0) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("参数代码数组不能为空");
            return response;
        }

        boolean result = propertyService.deleteByCodeBatch(codes);
        if (!result) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("删除系统参数失败，可能部分或全部参数不存在");
        }

        operateLogService.addNewLog(MENU_XTCSGL, ACTION_REMOVE, jsonData, response);
        return response;
    }

    @RequestMapping("/queryUserById")
    @ResponseBody
    public JSONResponse queryUserById(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String userid = jsonData.getString("userid");
        if (StringUtils.isBlank(userid)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("userid不能为空");
            return response;
        }
        JSONObject result = new JSONObject();
        result = commonService.cxgyxx(userid);
        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        if (response.getCode() > 0) {
            JSONObject data = new JSONObject();
            data.put("userInfo", result.getJSONArray("records"));
            response.setData(data);
        }
        return response;
    }

    /**
     * 获取坐席当天服务的客户列表
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 服务列表信息，包含客户姓名、服务时间和身份证号
     * @throws Exception 异常
     */
    @RequestMapping("/queryTodayServiceList")
    @ResponseBody
    public JSONResponse queryTodayServiceList(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse)
            throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String serveAgentId = jsonData.getString("serveAgentId");
        if (StringUtils.isBlank(serveAgentId)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("serveAgentId不能为空");
            return response;
        }

        return clientService.queryTodayServiceList(serveAgentId);
    }

    // ===================== 揭示信息管理接口 =====================

    /**
     * 获取揭示信息配置列表（分页）
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 揭示信息配置列表
     * @throws Exception 异常
     */
    @RequestMapping("/getCoworkScriptList")
    @ResponseBody
    public JSONResponse getCoworkScriptList(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String businessCode = jsonData.getString("businessCode");
        String initiateChannel = jsonData.getString("initiateChannel");
        Integer pageNumber = jsonData.getInteger("pageNumber");
        Integer pageSize = jsonData.getInteger("pageSize");

        if (pageNumber == null || pageSize == null) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("pageNumber和pageSize不能为空");
            return response;
        }

        response = coworkScriptService.getCoworkScriptList(businessCode, initiateChannel, pageNumber, pageSize);
        return response;
    }

    /**
     * 新增揭示信息配置
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 操作结果
     * @throws Exception 异常
     */
    @RequestMapping("/addCoworkScript")
    @ResponseBody
    public JSONResponse addCoworkScript(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "添加成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String businessCode = jsonData.getString("businessCode");
        if (StringUtils.isBlank(businessCode)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("businessCode不能为空");
            return response;
        }

        response = coworkScriptService.addCoworkScript(jsonData);
        operateLogService.addNewLog(MENU_XZHSPZ, ACTION_ADD, jsonData, response);
        return response;
    }

    /**
     * 更新揭示信息配置
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 操作结果
     * @throws Exception 异常
     */
    @RequestMapping("/updateCoworkScript")
    @ResponseBody
    public JSONResponse updateCoworkScript(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "更新成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String businessCode = jsonData.getString("businessCode");
        if (StringUtils.isBlank(businessCode)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("businessCode不能为空");
            return response;
        }

        response = coworkScriptService.updateCoworkScript(jsonData);
        operateLogService.addNewLog(MENU_XZHSPZ, ACTION_MODIFY, jsonData, response);
        return response;
    }

    /**
     * 删除揭示信息配置
     *
     * @param httpServletRequest  请求
     * @param httpServletResponse 响应
     * @return 操作结果
     * @throws Exception 异常
     */
    @RequestMapping("/deleteCoworkScript")
    @ResponseBody
    public JSONResponse deleteCoworkScript(HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "删除成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);

        String businessCode = jsonData.getString("businessCode");
        if (StringUtils.isBlank(businessCode)) {
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("businessCode不能为空");
            return response;
        }

        response = coworkScriptService.deleteCoworkScript(businessCode);
        operateLogService.addNewLog(MENU_XZHSPZ, ACTION_REMOVE, jsonData, response);
        return response;
    }
}
