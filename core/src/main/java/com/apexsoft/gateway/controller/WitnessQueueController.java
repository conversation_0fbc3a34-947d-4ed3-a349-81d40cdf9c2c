package com.apexsoft.gateway.controller;

import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.dispatch.client.ClientService;
import com.apexsoft.gateway.service.dispatch.queue.WitnessQueueService;
import com.apexsoft.gateway.service.opratelog.OperateLogService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 坐席查询接口，包括手动调度
 */
@RequestMapping("/witness/query")
@Controller
@Slf4j
public class WitnessQueueController implements Constant {

    @Autowired
    private WitnessQueueService queueService;

    @Autowired
    private ClientService clientService;

    @Autowired
    private OperateLogService operateLogService;

    /**
     * 坐席调度管理-查询队列情况
     * @param clientId
     * @param clientName
     * @param org
     * @return
     * @throws Exception
     */
    @RequestMapping("/queue")
    @ResponseBody
    @ApiOperation(value = "查询队列情况", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "查询队列成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse query(
            @ApiParam(name = "clientId", value = "用户id")
            @RequestParam(name = "clientId", required = false, defaultValue = "")
            String clientId,
            @ApiParam(name = "clientName", value = "客户姓名")
            @RequestParam(name = "clientName", required = false, defaultValue = "")
            String clientName,
            @ApiParam(name = "org", value = "营业部，多个用;隔开")
            @RequestParam(name = "org", required = false, defaultValue = "")
            String org)
            throws Exception {
        return queueService.queryQueue(clientId, clientName, org);
    }

    /**
     * 坐席任务查询
     * @param clientId
     * @param clientName
     * @param org
     * @param agentId
     * @param agentName
     * @param serveTimeBegin
     * @param serveTimeEnd
     * @param pageNumber
     * @param pageSize
     * @return
     * @throws Exception
     */
    @RequestMapping("/finishedWitness")
    @ResponseBody
    @ApiOperation(value = "查询已完成的见证流水", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "查询队列成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse witness(
            @ApiParam(name = "clientId", value = "用户id")
            @RequestParam(name = "clientId", required = false, defaultValue = "")
            String clientId,
            @ApiParam(name = "clientName", value = "客户姓名")
            @RequestParam(name = "clientName", required = false, defaultValue = "")
            String clientName,
            @ApiParam(name = "org", value = "营业部，多个用;隔开")
            @RequestParam(name = "org", required = false, defaultValue = "")
            String org,
            @ApiParam(name = "agentId", value = "坐席id")
            @RequestParam(name = "agentId", required = false, defaultValue = "")
            String agentId,
            @ApiParam(name = "agentName", value = "坐席姓名")
            @RequestParam(name = "agentName", required = false, defaultValue = "")
            String agentName,
            @ApiParam(name = "serveTimeBegin", value = "见证时间-上限")
            @RequestParam(name = "serveTimeBegin", required = false, defaultValue = "")
            String serveTimeBegin,
            @ApiParam(name = "serveTimeEnd", value = "见证时间-下限")
            @RequestParam(name = "serveTimeEnd", required = false, defaultValue = "")
            String serveTimeEnd,
            @ApiParam(name = "pageNumber", value = "pageNumber")
            @RequestParam(name = "pageNumber", required = false, defaultValue = "1")
            Integer pageNumber,
            @ApiParam(name = "pageSize", value = "pageSize")
            @RequestParam(name = "pageSize", required = false, defaultValue = "10")
            Integer pageSize)
            throws Exception {
        return clientService.queryFinishedWitness(
                clientId,
                clientName,
                org,
                agentId,
                agentName,
                serveTimeBegin,
                serveTimeEnd,
                pageNumber,
                pageSize
        );
    }

    /**
     * 置顶见证请求
     *
     * @param requestId
     * @return
     */
    @RequestMapping("/topRequest")
    @ResponseBody
    public JSONResponse topRequest(String requestId) {
        JSONResponse response = queueService.topRequest(requestId);
        operateLogService.addNewLog(MENU_ZXDDGL, ACTION_MODIFY, requestId, response);
        return response;
    }

}
