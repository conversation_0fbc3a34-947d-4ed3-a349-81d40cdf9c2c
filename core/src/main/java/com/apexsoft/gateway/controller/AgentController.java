package com.apexsoft.gateway.controller;

import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.common.utils.Constant;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.dispatch.agent.AgentService;
import com.apexsoft.gateway.service.dispatch.agent.om.AgentDetail;
import com.apexsoft.gateway.service.dispatch.agenttech.om.AgentTech;
import com.apexsoft.gateway.service.opratelog.OperateLogService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("/witness/agent")
@Controller
@Slf4j
public class AgentController implements Constant {

    @Autowired
    private AgentService agentService;

    @Autowired
    private OperateLogService operateLogService;

    @RequestMapping("/query")
    @ResponseBody
    @ApiOperation(value = "查询坐席", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "查询坐席成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse query(
            @ApiParam(name = "agentTech", value = "技能组对象", required = true)
            @RequestBody
            AgentDetail agentDetail)
            throws Exception {
        String agentId = agentDetail.getAgentId();
        String agentName = agentDetail.getAgentName();
        String org = agentDetail.getOrg() >= 0 ? agentDetail.getOrg() + "" : "";
        String techs = agentDetail.getTechs();
        Integer pageSize = agentDetail.getPageSize();
        Integer pageNumber = agentDetail.getPageNumber();
        String techName = agentDetail.getTechName();
        return agentService.queryAgent(
                agentId,
                agentName,
                org,
                techs,
                techName,
                pageNumber,
                pageSize
        );
    }

    @RequestMapping("/update")
    @ResponseBody
    @ApiOperation(value = "修改坐席技能组", response = JSONResponse.class, notes = "")
    @ApiResponses({ @ApiResponse(code = 201, message = "修改成功"), @ApiResponse(code = 401, message = "用户没有登录") })
    public JSONResponse updateAgent(
            @ApiParam(name = "userIds", value = "柜员号，多个用;隔开", required = true)
            String userIds,
            @ApiParam(name = "techCodes", value = "技能组编码，多个用;隔开", required = true)
            String techCodes)
            throws Exception {
        JSONResponse result = agentService.updateAgent(userIds, techCodes);
        JSONObject data = new JSONObject();
        data.put("userIds", userIds);
        data.put("techCodes", techCodes);
        operateLogService.addNewLog(MENU_JZRYGL, ACTION_MODIFY, data, result);
        return result;
    }
}
