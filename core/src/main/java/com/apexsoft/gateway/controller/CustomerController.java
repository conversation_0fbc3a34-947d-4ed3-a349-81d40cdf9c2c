package com.apexsoft.gateway.controller;


import com.alibaba.fastjson.JSONObject;

import com.apexsoft.gateway.aas.modules.index.service.DefaultServiceHandler;
import com.apexsoft.gateway.base.constant.Constant;
import com.apexsoft.gateway.common.redis.RedisDao;
import com.apexsoft.gateway.common.redis.RedisStringDao;
import com.apexsoft.gateway.common.utils.LdapUtil;
import com.apexsoft.gateway.common.utils.TLSSigAPIv2;
import com.apexsoft.gateway.common.utils.jwt.JwtUtil;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.TencentCloudService;

import com.apexsoft.gateway.service.dispatch.client.ClientService;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.Random;


@RequestMapping("/customer")
@Controller
@Slf4j
public class CustomerController {
    private DefaultServiceHandler defaultServiceHandler = new DefaultServiceHandler();

    @Autowired
    RedisDao redisDao;
    @Autowired
    RedisStringDao redisStringDao;

    @Autowired
    ClientService clientService;


    @Autowired
    private JwtUtil jwtUtil;
    @Autowired
    LdapUtil ldapUtil;
    @Autowired
    private TencentCloudService tencentCloudService;

    @Value("${tc-ai.trtc-SDKAppID}")
    private long SDKAppID;
    @Value("${tc-ai.trtc-SDKSecretKey}")
    private String SDKSecretKey;
    @Value("${tc-ai.usersign-expire}")
    private long expire;
    @Value("${tc-ai.appid}")
    private Long appid;

    private String validateToken(String token) {
        try {
            Claims user=jwtUtil.parseJWT(token);
            return user.getId();
        }catch (Exception e){
            log.error("Invalid token", e);
            return null;
        }
    }

    @RequestMapping(value = "/ttem",name = "临时访问凭证")
    @ResponseBody
    public JSONResponse ttem(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String token=jsonData.getString("token");
        String userId=validateToken(token);
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");

        if (StringUtils.isNotBlank(userId)) {
            try{
                JSONObject result = tencentCloudService.getTempToken();
                TLSSigAPIv2 api = new TLSSigAPIv2(SDKAppID, SDKSecretKey);
                Random random = new Random();
                int random6Digit = 100000 + random.nextInt(900000);  // 生成 6 位数
                String customerId=String.valueOf(random6Digit);
                String subId=String.valueOf(100000 + random.nextInt(900000));
                String subSign= api.genUserSig(subId, 180*86400);
                String customerSign= api.genUserSig(customerId, 180*86400);
                result.put("subSign", subSign);
                result.put("subId", subId);
                result.put("customerSign", customerSign);
                result.put("customerId", customerId);
                result.put("sdkAppId", SDKAppID);
                response.setData(result);
            }catch (Exception e){
                response = new CommonResponse(JSONResponse.CODE_FAIL, "查询失败:"+e.getMessage());
            }
        }else{
            response = new CommonResponse(JSONResponse.CODE_FAIL, "查询失败:"+"token无效");
        }

        return response;
    }

    @RequestMapping(value = "/start-record",name = "开始录制")
    @ResponseBody
    public JSONResponse startRecord(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String roomId=jsonData.getString("roomId");
        String userId=jsonData.getString("userId");
        String subId=jsonData.getString("subId");
        String recordMode=jsonData.getString("recordMode");


        String taskId = tencentCloudService.startCloudRecording(roomId, userId, subId, recordMode);

        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        JSONObject result = new JSONObject();
        result.put("taskId", taskId);
        response.setData(result);
        return response;
    }

    @RequestMapping(value = "/stop-record",name = "停止录制")
    @ResponseBody
    public JSONResponse stopRecord(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String taskId=jsonData.getString("taskId");
        tencentCloudService.stopCloudRecording(taskId);
        return new CommonResponse(JSONResponse.CODE_SUCCESS, "停止录制成功");
    }

    @RequestMapping(value = "/dismiss-room",name = "解散房间")
    @ResponseBody
    public JSONResponse DismissRoom(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        Long roomId = jsonData.getLong("roomId");
        try {
            tencentCloudService.dismissRoom(roomId);
            return new CommonResponse(JSONResponse.CODE_SUCCESS, "解散房间成功");
        } catch (Exception e) {
            return new CommonResponse(JSONResponse.CODE_SUCCESS, "解散房间成功");
        }
    }
    @RequestMapping("/modifyWitness")
    @ResponseBody
    public JSONResponse modifyWitness(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "修改成功");
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        Long id = jsonData.getLong("id");
//        String markerData = jsonData.getString("markerData");
        if(StringUtils.isBlank(id + "")){
            response.setCode(JSONResponse.CODE_FAIL);
            response.setNote("id不能为空");
            return response;
        }
        response = clientService.modifyWitness(jsonData);
        return response;
    }

    @RequestMapping(value = "/search-media",name = "查询并下载录制好的文件")
    @ResponseBody
    public JSONResponse SearchMedia(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        JSONObject jsonData = defaultServiceHandler.preHandle("", "", httpServletRequest, httpServletResponse);
        String roomId = jsonData.getString("roomId");

        JSONObject mediaInfo = tencentCloudService.searchMedia(roomId);
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        response.setData(mediaInfo);
        return response;
    }
}
