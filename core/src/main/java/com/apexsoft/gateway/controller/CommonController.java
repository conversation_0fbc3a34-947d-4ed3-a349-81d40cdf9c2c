package com.apexsoft.gateway.controller;


import com.alibaba.fastjson.JSONObject;

import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@RequestMapping("/common")
@Controller
@Slf4j
public class CommonController {


    @Autowired
    CommonService commonService;

    @RequestMapping("/getApplicationName")
    @ResponseBody
    public JSONResponse getApplicationName() {
        CommonResponse response = new CommonResponse(JSONResponse.CODE_SUCCESS, "查询成功");
        String lang = commonService.getDefaultCookieLang();
        JSONObject result = new JSONObject();

        response.setCode(result.getIntValue("code"));
        response.setNote(result.getString("note"));
        JSONObject xtcsObj=commonService.queryXtcs("cif.gtyw.yxsjqz");
        if(response.getCode()>0){
            JSONObject data = new JSONObject();
            data.put("name", result.getString("paramvalue"));
            response.setData(data);
        }
        return response;
    }

}
