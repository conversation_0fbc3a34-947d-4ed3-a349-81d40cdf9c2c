package com.apexsoft.gateway.controller;


import com.alibaba.fastjson.JSONObject;
import com.apexsoft.gateway.model.CommonResponse;
import com.apexsoft.gateway.model.JSONResponse;
import com.apexsoft.gateway.service.CommonService;
import com.apexsoft.gateway.common.redis.RedisStringDao;
import com.apexsoft.gateway.common.utils.NxRsaEncrypt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@RequestMapping("/cif")
@Controller
@Slf4j
public class CifController {

    @Autowired
    CommonService commonService;
    @Autowired
    RedisStringDao redisStringDao;

    @RequestMapping("/getCtrlToken")
    @ResponseBody
    public JSONResponse getCtrlToken() {
        CommonResponse result = new CommonResponse(JSONResponse.CODE_SUCCESS,"");
        JSONObject data = new JSONObject();
        data.put("token", NxRsaEncrypt.getToken());
        result.setData(data);
        return result;
    }


    @RequestMapping("/getUuid")
    @ResponseBody
    public JSONResponse getUuid(HttpServletRequest request, HttpServletResponse response) {
        CommonResponse result = new CommonResponse(JSONResponse.CODE_SUCCESS,"");
        JSONObject data = new JSONObject();
        data.put("uuid", commonService.setUUID(request));
        result.setData(data);
        return result;
    }




    public Boolean validateUUID(String uuid){
        if(StringUtils.isBlank(uuid)) return false;
        String ip=redisStringDao.getValue(uuid);
        return !StringUtils.isBlank(ip);
    }

    public JSONObject validateUuidObj(String uuid){
        JSONObject obj= null;
        if(StringUtils.isBlank(uuid)) return null;
        String str = redisStringDao.getValue(uuid);
        if(str!=null){
            obj=JSONObject.parseObject(redisStringDao.getValue(uuid));
        }
        return obj;
    }
}
