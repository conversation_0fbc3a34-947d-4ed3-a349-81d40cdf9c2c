//gradle插件
plugins {
    id 'java'
    //springBoot插件
    id 'org.springframework.boot' version "${springBootVersion}"
    //spring依赖管理插件
    id 'io.spring.dependency-management' version "${springDependencyManagement}"
    id 'java-library'
}

//springBoot构建buildInfo信息
springBoot {
    buildInfo()
}
dependencyManagement {
    imports {
        //springBoot的依赖管理模板
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        //springCloud的依赖管理，需要和SpringBoot配套
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        //LiveSupport的依赖管理
        mavenBom "com.apexsoft:live-spring-boot2-bom:${liveVersion}"
    }
}
dependencies {
    //基础
    api "com.apexsoft:live-spring-boot-starter:${liveVersion}"





    //log4j2 异步日志
    api "org.springframework.boot:spring-boot-starter-log4j2"
    api 'com.lmax:disruptor:3.4.4'

    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    api 'com.belerweb:pinyin4j:2.5.1'

    //gateway依赖包
    api "com.apexsoft.live-gateway:client:2.7.0-RC11"
    api "com.apexsoft.live-gateway:client:2.7.0-RC11"
    api "com.google.code.gson:gson:2.8.6"

    api "com.github.jai-imageio:jai-imageio-core:1.4.0"
    //pdfbox
    api "org.apache.pdfbox:pdfbox:3.0.0-alpha2"
    api "org.apache.pdfbox:fontbox:3.0.0-alpha2"

    api "com.apex.livebos.ws:livebos-ws-tianma:1.0-SNAPSHOT"
    api "com.apex:ams-livebos-client:1.3.3-SNAPSHOT"

    api 'commons-httpclient:commons-httpclient:3.1'
    api 'com.alibaba:fastjson:1.2.83'
    api "org.springframework.boot:spring-boot-starter-data-ldap:${springBootVersion}"
    implementation 'io.jsonwebtoken:jjwt-api:0.12.6'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.6'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.6'
    implementation 'com.corundumstudio.socketio:netty-socketio:2.0.3'

    implementation 'com.tencentcloudapi:tencentcloud-sdk-java:3.1.1149'
    implementation 'com.tencentcloudapi:tencentcloud-speech-sdk-java:1.0.53'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    api fileTree(dir: '../libs', includes: ['*.jar'])

    implementation group: 'mysql', name: 'mysql-connector-java', version: '6.0.6'

    implementation(project(":dispatcher"))

}

configurations {
    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    all*.exclude group: "org.slf4j", module: "slf4j-log4j12"
}

test {
    useJUnitPlatform()
}
