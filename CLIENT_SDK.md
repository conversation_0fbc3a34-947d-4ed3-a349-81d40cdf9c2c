## 客户端（pad端）接口

* 发起见证请求
  http://ip:8070/witness/client/addWitnessRequest?clientId=wyb2&org=100&bizRequestId=440192
    * clientId，可以传客户的唯一标识，比如姓名或者证件号码。
    * org，客户的营业部id
    * bizRequestId, 该笔业务请求的id，用于发送给座席端，座席端查询相关客户信息业务数据时使用

* 取消见证请求
  http://ip:8070/witness/client/cancelWitnessRequest?requestId=1
    * requestId，此次见证请求的id，addWitnessRequest接口返回的data.id

* 获取见证请求的当前状态
  http://ip:8070/witness/client/getWitnessRequest?requestId=1
    * requestId，此次见证请求的id，addWitnessRequest接口返回的data.id

## 客户端（pad端）如何使用接口

1. 客户端调用“发起见证请求”接口，加入见证等待队列
2. 客户端轮询调用“获取见证请求的当前状态”的接口（比如2秒一次），根据当前获取的状态进行相应的操作
   (1). 判断data.state：-1，客户主动取消请求；-2，无可服务坐席在线；-3，其他原因导致见证请求终止。如上三个状态都表示此次见证已结束，客户端需停止轮询。
   (2). data.state为0且data.serveAgentId为空，表示此时正在等待见证
   (3). data.state为1且data.serveAgentId不为空，表示已被坐席接受，前端可创建视频通话连接
   (4). 在(3)的前提下，data.result不为空，表示坐席已完成视频见证，客户端需停止轮询。（data.result为见证结果，true为成功，false为失败，data.reason表示见证的结果说明）