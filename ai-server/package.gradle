ext.module={
    name 'gateway-server'
}
group = "com.apexsoft"
version = '1.0.0'
bootJar {
    archiveBaseName = 'witness-server'
}
task deleteTemp(type: Delete,dependsOn :bootJar) {
    delete "build/${module.name}"
}



task logCopy(type: Copy, dependsOn :deleteTemp) {
    from('./') {
        include 'update.log'
    }
    into 'livecd/conf'
}
task configCopy(type: Copy, dependsOn :logCopy) {
    from('src/main/resources') {
        include '*.yml'
        include '*.xml'
        include 'cert/**/*'
    }
    into 'livecd/conf/config'
}


task livecdCopy(type: Copy, dependsOn :configCopy) {
    from('livecd')
    into "build/${module.name}/livecd"
}

task zipCopy(type: Copy, dependsOn: livecdCopy) {

    from('build/libs') {
        include "${module.name}-${version}.jar"
        rename '(.*)',"${module.name}.jar"
    }
    into "build/${module.name}"
}
//全量打包
task zipAllPackage(type: Zip,dependsOn :zipCopy) {
    from 'build'
    include "${module.name}/**"
    destinationDir file('build/libs')
    baseName "${module.name}"
    version "${module.version}"
    extension 'zip'
}
//增量打包
task zipAddPackage(type: Zip,dependsOn :zipCopy) {
    from 'build'
    include "${module.name}/livecd/conf/update.log"
    include "${module.name}/livecd/conf/package-info.properties"
    include "${module.name}/${module.name}.jar"
    destinationDir file('build/libs')
    baseName "${module.name}"
    version "${version}"
    extension 'zip'
}

