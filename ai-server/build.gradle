import java.text.SimpleDateFormat

//gradle插件
plugins {
    id 'java'
    //springBoot插件
    id 'org.springframework.boot' version "${springBootVersion}"
    //spring依赖管理插件
    id 'io.spring.dependency-management' version "${springDependencyManagement}"

    id 'war'
}

//springBoot构建buildInfo信息
springBoot {
    buildInfo()
}
dependencyManagement {
    imports {
        //springBoot的依赖管理模板
        mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        //springCloud的依赖管理，需要和SpringBoot配套
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        //LiveSupport的依赖管理
        mavenBom "com.apexsoft:live-spring-boot2-bom:${liveVersion}"
    }
}
dependencies {
    providedCompile('org.springframework.boot:spring-boot-starter-tomcat')

    compileOnly 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'

    implementation(project(":core"))
}

configurations {
    all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    all*.exclude group: "org.slf4j", module: "slf4j-log4j12"
}

test {
    useJUnitPlatform()
}

/*** 删除无用的build文件*/
task prodWar(dependsOn: war) {
    doLast {
        def tempDir = "${buildDir}\\buildtemp\\"
        def srcWar = "${buildDir}\\libs\\" + this.name + "-" + version + "-plain.war"
        println srcWar
        def destWar = "${buildDir}\\libs\\final\\"+this.name+".war"
        delete(tempDir)
        mkdir(tempDir)
        ant.unzip(src: srcWar, dest: tempDir)
        //删除yml文件
        def tempDirClass = tempDir + "\\WEB-INF\\classes\\"
        FileTree tree = fileTree(dir: tempDirClass)
        tree.each {File file ->
            if(file.name.endsWith(".yml")){
                file.delete();
                println '删除配置文件：'+file.name
            }
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss")
        def nowDate = sdf.format(new Date())
        ant.war(destfile: destWar, needxmlfile: false) {
            fileset(dir: tempDir, includes: "**/*")
            manifest {
                attributes name: "Manifest-Version", value: version
                attributes name: "Build-Date", value: nowDate
            }
        }
    }
}

//publish.dependsOn bootJar
apply {from 'package.gradle'}