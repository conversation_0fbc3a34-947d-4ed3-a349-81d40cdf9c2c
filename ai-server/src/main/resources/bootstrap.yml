ams:
  config:
    enabled: false
  # 证书信任域
  authority: livebos-server
  # 根证书
  ca:
    certFile: classpath:cert/ca.pem
  # 客户端证书
  client:
    certFile: classpath:cert/client.pem
    keyFile: classpath:cert/client.pkcs8.pem
    keepAliveTime: 60
    keepAliveWithoutCalls: true
  # 服务端证书
  server:
    certFile: classpath:cert/server.pem
    keyFile: classpath:cert/server.pkcs8.pem
    namespace: zzgj #微服务命名空间
  #注册中心

  
  registry:
    protocol: zk
    address: ***************:2181
  metrics:
    prometheus:
      sd:
        type: consul
      port: 40097
      trust-scrape-ips: ***************
