application:
  name: ai-demo
  debug: true
  imageDic: /Users/<USER>/fe/apex/zhihuiyunying/witnessServerImageCache
  tempDic: /Users/<USER>/apexsoft/pad/public/ai-server/temp/
  YGT3_URL: 'http://***************:8080'
server:
  port: 8070
  livebos-path: /
  servlet:
    session:
      timeout: 7200
    context-path: /
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    jdbcUrl: *******************************************************************************
    url: *******************************************************************************
    username: root
    password: 123456
    hikari:
      maximum-pool-size: 10
      connection-timeout: 30000
      idle-timeout: 600000
      pool-name: HikariPool-1
  session:
    store-type: none
  redis:
    database: 0
    #公司配置
    host: ***************
    port: 6379
    password: apexsoft
    pool:
      max-active: 100
      max-idle: 5
      max-wait: 60000
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 500MB

aas:
  session:
    share:
      enabled: true
    forceOffline: false
  authentication:
  #enabled: true
  #type: livebos
  swagger:
    enabled: true
    showDemo: false
  amc:
    enabled: true
live:
  swagger:
    enabled: false
  actuator:
    enabled: true
  service:
    security:
      exclude:
        protocols: none
    grpc:
      mode: proto
  livebos:
    username: webuser
    password: 888888
    namespace: livebos-server
    algorithm: ""
    scheme: ""
    securityCode: ""

management:
  server:
    port: -1
esb:
  ip: *************
  port: 7002
  user: admin
  pwd: 96e79218965eb72c92a549dd5a330112

tc-ai:
  trtc-SDKAppID: 1600059686  #trtc应用id  https://console.cloud.tencent.com/trtc/app
  trtc-SDKSecretKey: fc0cff0b4c1cd192c2940923c2bbbc02d063c695121448e24a9f16aaabfdea9c   #trtc应用SDKSecretKey
  SecretId: AKIDP7ctwlaLsLejZCcqqbmjRu9C1cy86qLp  # 腾讯云帐号SecretId  可申请子帐号  https://console.cloud.tencent.com/cam/capi
  SecretKey: OZWIsGQN6cw2zqY4CCzax8B3mIad1IMC   # 腾讯云帐号SecretKey https://console.cloud.tencent.com/cam/capi
  usersign-expire: 86400   #asr临时签名过期时间
  appid: 1307915795  # 腾讯云帐号appid https://console.cloud.tencent.com/cam/capi
app:
  redis_cache_prefix: QUERY_JZXT_
ws:
  server:
    port: 8090
    host: **************
  witness:
    port: 8010
  jwt:
    key: apexydzybzb
    #    10分钟
    ttl: 3000
mybatis-plus:
  configuration:
    cache-enabled: false
    local-cache-scope: statement
  mapper-locations: classpath:**/dao/**